package com.example.myapplication.features.profile.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.features.profile.domain.model.ProfileResponse
import com.example.myapplication.features.profile.domain.repository.ProfileRepository

/**
 * Caso de uso para obter perfil do usuário
 * 
 * Seguindo SRP: Responsabilidade única de executar lógica de obtenção de perfil
 * Seguindo DIP: Depende da abstração ProfileRepository
 */
class GetProfileUseCase(
    private val profileRepository: ProfileRepository
) {
    
    private val logger = AppLogger.getInstance()
    
    /**
     * Executa a obtenção do perfil do usuário
     * 
     * @return Resource contendo dados do perfil ou erro
     */
    suspend operator fun invoke(): Resource<ProfileResponse> {
        return try {
            logger.info("GetProfileUseCase", "Iniciando busca de perfil do usuário")
            
            val result = profileRepository.getProfile()
            
            when (result) {
                is Resource.Success -> {
                    logger.info("GetProfileUseCase", "Perfil obtido com sucesso")
                    validateProfileData(result.data!!)
                }
                is Resource.Error -> {
                    logger.warning("GetProfileUseCase", "Erro ao obter perfil: ${result.message}")
                    result
                }
                is Resource.Loading -> {
                    result
                }
            }
            
        } catch (e: Exception) {
            val errorMessage = "Erro inesperado ao buscar perfil: ${e.message}"
            logger.error("GetProfileUseCase", errorMessage, e)
            Resource.Error(errorMessage)
        }
    }
    
    /**
     * Valida os dados do perfil recebidos
     */
    private fun validateProfileData(profile: ProfileResponse): Resource<ProfileResponse> {
        return try {
            // Validações básicas
            when {
                profile.username.isBlank() -> {
                    logger.warning("GetProfileUseCase", "Username vazio no perfil")
                    Resource.Error("Dados de perfil inválidos: username vazio")
                }
                profile.email.isBlank() -> {
                    logger.warning("GetProfileUseCase", "Email vazio no perfil")
                    Resource.Error("Dados de perfil inválidos: email vazio")
                }
                profile.fullName.isBlank() -> {
                    logger.warning("GetProfileUseCase", "Nome completo vazio no perfil")
                    Resource.Error("Dados de perfil inválidos: nome vazio")
                }
                else -> {
                    logger.info("GetProfileUseCase", "Dados de perfil validados com sucesso")
                    Resource.Success(profile)
                }
            }
        } catch (e: Exception) {
            logger.error("GetProfileUseCase", "Erro na validação dos dados de perfil", e)
            Resource.Error("Erro na validação dos dados de perfil")
        }
    }
    
    /**
     * Verifica se o perfil tem estatísticas válidas
     */
    fun hasValidStats(profile: ProfileResponse): Boolean {
        return profile.stats != null && profile.stats.readingStats.totalWorks >= 0
    }
    
    /**
     * Obtém estatísticas formatadas para exibição
     */
    fun getFormattedStats(profile: ProfileResponse): Map<String, String> {
        val stats = profile.stats
        return if (stats != null) {
            mapOf(
                "totalBooks" to stats.readingStats.totalWorks.toString(),
                "completed" to stats.readingStats.completed.toString(),
                "reading" to stats.readingStats.reading.toString(),
                "planToRead" to stats.readingStats.planToRead.toString(),
                "totalChapters" to stats.totalChaptersRead.toString(),
                "totalReviews" to stats.totalReviews.toString(),
                "averageRating" to (stats.averageRating?.let { "%.1f".format(it) } ?: "N/A"),
                "favoriteGenre" to (stats.favoriteGenre ?: "Não definido"),
                "averageReadingTime" to (stats.averageReadingTime?.let { "%.1f min".format(it) } ?: "N/A")
            )
        } else {
            mapOf(
                "totalBooks" to "0",
                "completed" to "0",
                "reading" to "0",
                "planToRead" to "0",
                "totalChapters" to "0",
                "totalReviews" to "0",
                "averageRating" to "N/A",
                "favoriteGenre" to "Não definido",
                "averageReadingTime" to "N/A"
            )
        }
    }
}
