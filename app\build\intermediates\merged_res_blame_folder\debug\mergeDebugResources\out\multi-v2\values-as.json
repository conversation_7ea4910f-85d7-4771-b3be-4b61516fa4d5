{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-47:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,309,417,522,626,726,8415", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "201,304,412,517,621,721,850,8511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1021,1106,1185,1260,1335,1409,1484,1550", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1016,1101,1180,1255,1330,1404,1479,1545,1663"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,950,1035,1128,1226,1313,1410,7765,7854,7944,8027,8112,8191,8266,8341,8516,8591,8657", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "945,1030,1123,1221,1308,1405,1504,7849,7939,8022,8107,8186,8261,8336,8410,8586,8652,8770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8775,8860", "endColumns": "84,87", "endOffsets": "8855,8943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4653,4744,4836,4945,5025,5110,5211,5316,5414,5516,5603,5710,5809,5913,6034,6114,6217", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4648,4739,4831,4940,5020,5105,5206,5311,5409,5511,5598,5705,5804,5908,6029,6109,6212,6306"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1509,1628,1748,1861,1979,2075,2169,2280,2424,2545,2687,2772,2870,2965,3064,3180,3308,3411,3542,3672,3801,3981,4101,4219,4343,4476,4572,4668,4789,4915,5012,5122,5230,5366,5510,5620,5722,5799,5900,6001,6107,6198,6290,6399,6479,6564,6665,6770,6868,6970,7057,7164,7263,7367,7488,7568,7671", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,105,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "1623,1743,1856,1974,2070,2164,2275,2419,2540,2682,2767,2865,2960,3059,3175,3303,3406,3537,3667,3796,3976,4096,4214,4338,4471,4567,4663,4784,4910,5007,5117,5225,5361,5505,5615,5717,5794,5895,5996,6102,6193,6285,6394,6474,6559,6660,6765,6863,6965,7052,7159,7258,7362,7483,7563,7666,7760"}}]}]}