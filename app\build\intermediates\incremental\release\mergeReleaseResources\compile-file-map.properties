#Fri Jun 20 11:45:49 BRT 2025
com.example.myapplication.app-main-46\:/drawable/ic_launcher_background.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_background.xml.flat
com.example.myapplication.app-main-46\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable_ic_launcher_foreground.xml.flat
com.example.myapplication.app-main-46\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.myapplication.app-main-46\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.myapplication.app-main-46\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-46\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-46\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-46\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.myapplication.app-main-46\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.myapplication.app-main-46\:/xml/backup_rules.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_backup_rules.xml.flat
com.example.myapplication.app-main-46\:/xml/data_extraction_rules.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_data_extraction_rules.xml.flat
com.example.myapplication.app-main-46\:/xml/network_security_config.xml=C\:\\Users\\kevin.damm\\Documents\\mobile\\MyApplication\\app\\build\\intermediates\\merged_res\\release\\mergeReleaseResources\\xml_network_security_config.xml.flat
