package com.example.myapplication.core.logging

/**
 * Interface para serviço de logging
 * 
 * Seguindo SRP: Responsabilidade única de definir contratos de logging
 * Seguindo DIP: Abstração para facilitar testes e diferentes implementações
 */
interface ILoggerService {
    
    /**
     * Log de debug - informações detalhadas para desenvolvimento
     */
    fun debug(tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * Log de informação - eventos importantes do sistema
     */
    fun info(tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * Log de warning - situações que merecem atenção mas não são erros
     */
    fun warning(tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * Log de erro - falhas que afetam funcionalidade
     */
    fun error(tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * Log crítico - falhas graves que podem afetar a aplicação
     */
    fun critical(tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * Log de autenticação - eventos específicos de auth
     */
    fun auth(message: String, throwable: Throwable? = null)
    
    /**
     * Log de rede - eventos de requisições HTTP
     */
    fun network(message: String, throwable: Throwable? = null)
    
    /**
     * Log de segurança - eventos relacionados à segurança
     */
    fun security(message: String, throwable: Throwable? = null)
}

/**
 * Níveis de log para controle de verbosidade
 */
enum class LogLevel(val priority: Int) {
    DEBUG(1),
    INFO(2),
    WARNING(3),
    ERROR(4),
    CRITICAL(5);
    
    companion object {
        fun fromString(level: String): LogLevel {
            return when (level.uppercase()) {
                "DEBUG" -> DEBUG
                "INFO" -> INFO
                "WARNING", "WARN" -> WARNING
                "ERROR" -> ERROR
                "CRITICAL" -> CRITICAL
                else -> INFO
            }
        }
    }
}

/**
 * Configuração do sistema de logging
 */
data class LogConfig(
    val minLevel: LogLevel = LogLevel.DEBUG,
    val enableConsoleLogging: Boolean = true,
    val enableFileLogging: Boolean = false,
    val enableCrashlytics: Boolean = false,
    val logToAnalytics: Boolean = false,
    val maxLogFileSize: Long = 5 * 1024 * 1024, // 5MB
    val maxLogFiles: Int = 3
)
