package com.example.myapplication.features.main.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Notifications
import androidx.compose.material.icons.outlined.Person
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.domain.model.UserLogin
import com.example.myapplication.features.notifications.presentation.NotificationsScreen
import com.example.myapplication.features.profile.presentation.ProfileScreen
import com.example.myapplication.features.settings.presentation.SettingsScreen
import com.example.myapplication.ui.theme.MyApplicationTheme
/**
 * Tela principal da aplicação com navegação bottom
 *
 * Seguindo SRP: Responsabilidade única de gerenciar navegação principal
 * Seguindo OCP: Extensível para novas abas de navegação
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    userFullName: String = "Usuário",
    currentUser: UserLogin? = null,
    appContainer: com.example.myapplication.core.di.AppContainer,
    onLogout: () -> Unit = {},
    onNavigateToDebug: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    var selectedTab by remember { mutableStateOf(0) }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEachIndexed { index, item ->
                    NavigationBarItem(
                        icon = {
                            Icon(
                                imageVector = if (selectedTab == index) item.selectedIcon else item.unselectedIcon,
                                contentDescription = item.title
                            )
                        },
                        label = { Text(item.title) },
                        selected = selectedTab == index,
                        onClick = { selectedTab = index }
                    )
                }
            }
        }
    ) { paddingValues ->
        when (selectedTab) {
            0 -> HomeScreen(
                userFullName = userFullName,
                modifier = Modifier.padding(paddingValues)
            )
            1 -> NotificationsScreen(
                modifier = Modifier.padding(paddingValues)
            )
            2 -> ProfileScreen(
                currentUser = currentUser,
                appContainer = appContainer,
                onLogout = onLogout,
                modifier = Modifier.padding(paddingValues)
            )
            3 -> SettingsScreen(
                modifier = Modifier.padding(paddingValues)
            )
        }

        // FAB para debug (apenas em desenvolvimento)
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            contentAlignment = Alignment.BottomEnd
        ) {
            if (selectedTab == 0) { // Apenas na aba Home
                FloatingActionButton(
                    onClick = onNavigateToDebug,
                    containerColor = MaterialTheme.colorScheme.secondary
                ) {
                    Text("🐛", fontSize = 20.sp)
                }
            }
        }
    }
}

// Data class para itens da navegação bottom
data class BottomNavItem(
    val title: String,
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector
)

// Lista de itens da navegação bottom
private val bottomNavItems = listOf(
    BottomNavItem(
        title = "Home",
        selectedIcon = Icons.Filled.Home,
        unselectedIcon = Icons.Outlined.Home
    ),
    BottomNavItem(
        title = "Notificações",
        selectedIcon = Icons.Filled.Notifications,
        unselectedIcon = Icons.Outlined.Notifications
    ),
    BottomNavItem(
        title = "Perfil",
        selectedIcon = Icons.Filled.Person,
        unselectedIcon = Icons.Outlined.Person
    ),
    BottomNavItem(
        title = "Configurações",
        selectedIcon = Icons.Filled.Settings,
        unselectedIcon = Icons.Outlined.Settings
    )
)

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    MyApplicationTheme {
        // Preview não pode usar AppContainer real
        Text("Main Screen Preview - Requer AppContainer")
    }
}
