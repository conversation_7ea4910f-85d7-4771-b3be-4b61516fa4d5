package com.example.myapplication.core.network

import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.io.IOException

/**
 * Interceptor temporário para capturar JSO<PERSON> das respostas
 */
class ResponseLoggingInterceptor : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        
        // Apenas para endpoint de perfil
        if (request.url.toString().contains("auth/profile")) {
            val responseBody = response.body
            if (responseBody != null) {
                val responseBodyString = responseBody.string()
                println("=== RESPONSE JSON FOR ${request.url} ===")
                println(responseBodyString)
                println("=== END RESPONSE JSON ===")
                
                // Recriar o response body para não consumir o stream
                val newResponseBody = responseBodyString.toResponseBody(responseBody.contentType())
                return response.newBuilder()
                    .body(newResponseBody)
                    .build()
            }
        }
        
        return response
    }
}
