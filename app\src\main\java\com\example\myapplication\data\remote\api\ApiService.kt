package com.example.myapplication.data.remote.api

import com.example.myapplication.data.remote.dto.BaseRequestDto
import com.example.myapplication.data.remote.dto.BaseResponseDto
import com.example.myapplication.data.remote.dto.LoginRequestDto
import com.example.myapplication.data.remote.dto.LoginResponseDto
import com.example.myapplication.data.remote.dto.RegisterRequestDto
import com.example.myapplication.data.remote.dto.UserDto
import com.example.myapplication.features.profile.domain.model.ProfileResponse
import retrofit2.Response
import retrofit2.http.*

/**
 * Interface que define os endpoints da API para o aplicativo.
 * Esta interface será implementada pelo Retrofit para criar as requisições HTTP reais.
 */
interface ApiService {    /**
     * Endpoint para autenticação de usuário
     * @param request Credenciais de login (usernameOrEmail e password)
     * @return Resposta contendo tokens de acesso e dados do usuário
     */
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequestDto): Response<BaseResponseDto<LoginResponseDto>>

    /**
     * Endpoint para registro de novo usuário
     * @param request Dados para registro (username, email, password, fullName)
     * @return Resposta contendo tokens de acesso e dados do usuário criado
     */
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequestDto): Response<BaseResponseDto<LoginResponseDto>>

    /**
     * Endpoint para obter perfil do usuário autenticado
     * @return Resposta contendo dados completos do perfil e estatísticas
     */
    @GET("auth/profile")
    suspend fun getProfile(): Response<ProfileResponse>

}