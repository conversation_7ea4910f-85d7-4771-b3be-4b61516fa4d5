package com.example.myapplication.core.navigation

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.login.LoginViewModelFactory
import com.example.myapplication.features.auth.presentation.register.RegisterResult
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory
import com.example.myapplication.features.auth.presentation.network.NetworkTestScreen
import com.example.myapplication.features.main.presentation.MainScreen
import com.example.myapplication.features.splash.presentation.SplashScreen
import com.example.myapplication.features.splash.presentation.SplashScreenWithError
import com.example.myapplication.features.debug.presentation.DebugScreen
import kotlinx.coroutines.launch

/**
 * Componente de navegação da aplicação
 *
 * Seguindo SRP: Responsabilidade única de gerenciar navegação
 * Seguindo OCP: Extensível para novos fluxos de navegação
 */
@Composable
fun AppNavigation(
    onShowToast: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val appContainer = remember { AppContainer(context) }
    val coroutineScope = rememberCoroutineScope()

    // Estados reativos do AuthenticationManager
    val isAuthenticated by appContainer.authenticationManager.isAuthenticated.collectAsState()
    val currentUser by appContainer.authenticationManager.currentUser.collectAsState()

    val currentScreen = remember { mutableStateOf<Screen>(Screen.Splash) }
    val isLoading = remember { mutableStateOf(true) }
    val errorMessage = remember { mutableStateOf<String?>(null) }

    // Verificar estado de autenticação na inicialização
    LaunchedEffect(Unit) {
        try {
            val isUserAuthenticated = appContainer.authenticationManager.checkAuthenticationStatus()

            if (isUserAuthenticated) {
                // Iniciar serviço de refresh automático apenas se autenticado
                appContainer.authenticationManager.startAutoRefresh()
            }

            // Simular um pequeno delay para mostrar a splash screen
            kotlinx.coroutines.delay(1500)

            isLoading.value = false
            if (isUserAuthenticated) {
                currentScreen.value = Screen.Main
            } else {
                currentScreen.value = Screen.Login
            }
        } catch (e: Exception) {
            isLoading.value = false
            errorMessage.value = "Erro ao verificar autenticação: ${e.message}"
        }
    }

    // Observar mudanças no estado de autenticação
    LaunchedEffect(isAuthenticated) {
        if (!isAuthenticated && currentScreen.value == Screen.Main) {
            currentScreen.value = Screen.Login
        }
    }

    when (currentScreen.value) {
        Screen.Splash -> {
            if (errorMessage.value != null) {
                SplashScreenWithError(
                    errorMessage = errorMessage.value!!,
                    onRetry = {
                        errorMessage.value = null
                        isLoading.value = true
                        coroutineScope.launch {
                            try {
                                val isUserAuthenticated = appContainer.authenticationManager.checkAuthenticationStatus()
                                kotlinx.coroutines.delay(1000)
                                isLoading.value = false
                                if (isUserAuthenticated) {
                                    currentScreen.value = Screen.Main
                                } else {
                                    currentScreen.value = Screen.Login
                                }
                            } catch (e: Exception) {
                                isLoading.value = false
                                errorMessage.value = "Erro ao verificar autenticação: ${e.message}"
                            }
                        }
                    },
                    modifier = modifier
                )
            } else {
                SplashScreen(
                    isLoading = isLoading.value,
                    loadingMessage = if (isLoading.value) "Verificando autenticação..." else "Redirecionando...",
                    modifier = modifier
                )
            }
        }

        Screen.Login -> {
            val loginViewModel: LoginViewModel = viewModel(
                factory = LoginViewModelFactory(appContainer)
            )

            LoginScreen(
                viewModel = loginViewModel,
                onLoginSuccess = { result: LoginResult.Success ->
                    onShowToast("Login realizado com sucesso! Bem-vindo, ${result.loginResponse.user.fullName}")
                    currentScreen.value = Screen.Main
                },
                onShowNetworkTest = {
                    currentScreen.value = Screen.NetworkTest
                },
                onNavigateToRegister = {
                    currentScreen.value = Screen.Register
                },
                modifier = modifier
            )
        }

        Screen.Register -> {
            val registerViewModel: RegisterViewModel = viewModel(
                factory = RegisterViewModelFactory(appContainer)
            )

            RegisterScreen(
                viewModel = registerViewModel,
                onRegisterSuccess = { result: RegisterResult.Success ->
                    onShowToast("Conta criada com sucesso! Bem-vindo, ${result.registerResponse.user.fullName}")
                    currentScreen.value = Screen.Main
                },
                onNavigateToLogin = {
                    currentScreen.value = Screen.Login
                },
                modifier = modifier
            )
        }

        Screen.NetworkTest -> {
            NetworkTestScreen()
        }

        Screen.Main -> {
            MainScreen(
                userFullName = currentUser?.fullName ?: "Usuário",
                currentUser = currentUser,
                onLogout = {
                    coroutineScope.launch {
                        try {
                            // Parar serviço de refresh automático
                            appContainer.authenticationManager.stopAutoRefresh()

                            appContainer.authenticationManager.logout()
                            currentScreen.value = Screen.Login
                            onShowToast("Logout realizado com sucesso!")
                        } catch (e: Exception) {
                            onShowToast("Erro durante logout: ${e.message}")
                        }
                    }
                },
                onNavigateToDebug = {
                    currentScreen.value = Screen.Debug
                },
                modifier = modifier
            )
        }

        Screen.Debug -> {
            DebugScreen(
                appContainer = appContainer,
                onNavigateBack = {
                    currentScreen.value = Screen.Main
                },
                modifier = modifier
            )
        }

        else -> {
            // Fallback para tela de login
            currentScreen.value = Screen.Login
        }
    }
}
