package com.example.myapplication.features.profile.domain.model

/**
 * Modelo de resposta da API de perfil
 */
data class ProfileResponse(
    val id: Long,
    val username: String,
    val email: String,
    val fullName: String,
    val avatar: String?,
    val isActive: Boolean,
    val role: String,
    val createdAt: String,
    val updatedAt: String,
    val stats: ProfileStats?
)

/**
 * Estatísticas do perfil do usuário
 */
data class ProfileStats(
    val readingStats: ReadingStats,
    val totalChaptersRead: Int,
    val totalLists: Int,
    val totalPublicLists: Int,
    val totalRankings: Int,
    val totalPublicRankings: Int,
    val totalReviews: Int,
    val totalPublicReviews: Int,
    val averageRating: Double?,
    val totalReviewLikes: Int,
    val averageReadingTime: Double?,
    val lastReadingActivity: String?,
    val favoriteGenre: String?
)

/**
 * Estatísticas de leitura
 */
data class ReadingStats(
    val reading: Int,
    val completed: Int,
    val dropped: Int,
    val planToRead: Int,
    val onHold: Int,
    val totalWorks: Int
)

/**
 * DTO para requisição de perfil (se necessário)
 */
data class ProfileRequestDto(
    val includeStats: Boolean = true
)

/**
 * Modelo de erro da API de perfil
 */
data class ProfileErrorResponse(
    val statusCode: Int,
    val message: String,
    val error: String,
    val timestamp: String,
    val path: String
)
