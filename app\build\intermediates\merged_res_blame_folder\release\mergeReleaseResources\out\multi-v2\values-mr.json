{"logs": [{"outputFile": "com.example.myapplication.app-mergeReleaseResources-43:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,309,410,513,615,720,8353", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "200,304,405,508,610,715,832,8449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,986,1071,1142,1218,1294,1370,1446,1512", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,981,1066,1137,1213,1289,1365,1441,1507,1625"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,931,1013,1110,1208,1295,1381,7717,7806,7889,7969,8054,8125,8201,8277,8454,8530,8596", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,75,75,75,75,65,117", "endOffsets": "926,1008,1105,1203,1290,1376,1461,7801,7884,7964,8049,8120,8196,8272,8348,8525,8591,8709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1466,1591,1714,1825,1950,2053,2153,2268,2404,2527,2673,2758,2864,2955,3053,3167,3297,3408,3543,3677,3805,3983,4108,4224,4343,4468,4560,4655,4775,4904,5004,5107,5216,5353,5495,5610,5708,5784,5887,5991,6098,6183,6273,6373,6453,6536,6635,6734,6831,6930,7017,7121,7221,7325,7443,7523,7623", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "1586,1709,1820,1945,2048,2148,2263,2399,2522,2668,2753,2859,2950,3048,3162,3292,3403,3538,3672,3800,3978,4103,4219,4338,4463,4555,4650,4770,4899,4999,5102,5211,5348,5490,5605,5703,5779,5882,5986,6093,6178,6268,6368,6448,6531,6630,6729,6826,6925,7012,7116,7216,7320,7438,7518,7618,7712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8714,8799", "endColumns": "84,84", "endOffsets": "8794,8879"}}]}]}