package com.example.myapplication.core.security

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.domain.model.AuthToken
import com.example.myapplication.domain.model.UserLogin
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Gerenciador de tokens de autenticação com armazenamento seguro
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar tokens e estado de autenticação
 * Seguindo OCP: Extensível para novos tipos de autenticação
 * Seguindo DIP: Interface abstrata para facilitar testes
 * 
 * Implementa:
 * - Armazenamento seguro com EncryptedSharedPreferences
 * - Gerenciamento de estado reativo com StateFlow
 * - Singleton thread-safe
 * - Verificação automática de expiração de tokens
 */
interface ITokenManagerService {
    val isAuthenticated: StateFlow<Boolean>
    val currentUser: StateFlow<UserLogin?>
    
    suspend fun saveTokens(authToken: AuthToken, user: UserLogin)
    suspend fun getAccessToken(): String?
    suspend fun getRefreshToken(): String?
    suspend fun clearTokens()
    suspend fun isTokenValid(): Boolean
    suspend fun updateUser(user: UserLogin)
    suspend fun getTokenExpirationInfo(): Pair<Long, Long>? // (tokenSavedAt, expiresIn)
}

class TokenManager private constructor(
    private val context: Context
) : ITokenManagerService {

    companion object {
        @Volatile
        private var INSTANCE: TokenManager? = null

        fun getInstance(context: Context): TokenManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TokenManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val logger = AppLogger.getInstance()
    
    // Encrypted SharedPreferences para armazenamento seguro
    private val encryptedPrefs: SharedPreferences by lazy {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
            
        EncryptedSharedPreferences.create(
            context,
            AppConstants.Preferences.USER_PREFERENCES,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    // Estados reativos
    private val _isAuthenticated = MutableStateFlow(false)
    override val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()
    
    private val _currentUser = MutableStateFlow<UserLogin?>(null)
    override val currentUser: StateFlow<UserLogin?> = _currentUser.asStateFlow()
    
    init {
        // Verificar estado de autenticação na inicialização
        logger.info("TokenManager", "TokenManager inicializado")
        checkAuthenticationStatus()
    }
    
    /**
     * Salva tokens e dados do usuário de forma segura
     */
    override suspend fun saveTokens(authToken: AuthToken, user: UserLogin) {
        try {
            logger.info("TokenManager", "Salvando tokens para usuário: ${user.username}")

            encryptedPrefs.edit().apply {
                putString(AppConstants.Preferences.ACCESS_TOKEN, authToken.accessToken)
                putString(AppConstants.Preferences.REFRESH_TOKEN, authToken.refreshToken)
                putString("token_type", authToken.tokenType)
                putLong("expires_in", authToken.expiresIn)
                putLong("token_saved_at", System.currentTimeMillis())
                putBoolean(AppConstants.Preferences.IS_LOGGED_IN, true)

                // Salvar dados do usuário
                putLong("user_id", user.id)
                putString("user_username", user.username)
                putString("user_email", user.email)
                putString("user_full_name", user.fullName)
                putString("user_avatar", user.avatar)
                putBoolean("user_is_active", user.isActive)
                putString("user_created_at", user.createdAt)
                putString("user_updated_at", user.updatedAt)

                apply()
            }

            _currentUser.value = user
            _isAuthenticated.value = true

            logger.auth("Login bem-sucedido para usuário: ${user.username}")

        } catch (e: Exception) {
            logger.error("TokenManager", "Falha ao salvar tokens", e)
            // Log error e limpar estado em caso de falha
            clearTokens()
            throw SecurityException("Falha ao salvar tokens de forma segura", e)
        }
    }
    
    /**
     * Recupera o access token se válido
     */
    override suspend fun getAccessToken(): String? {
        return if (isTokenValid()) {
            encryptedPrefs.getString(AppConstants.Preferences.ACCESS_TOKEN, null)
        } else {
            null
        }
    }
    
    /**
     * Recupera o refresh token
     */
    override suspend fun getRefreshToken(): String? {
        return encryptedPrefs.getString(AppConstants.Preferences.REFRESH_TOKEN, null)
    }
    
    /**
     * Verifica se o token ainda é válido baseado no tempo de expiração
     */
    override suspend fun isTokenValid(): Boolean {
        val isLoggedIn = encryptedPrefs.getBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)
        if (!isLoggedIn) {
            logger.debug("TokenManager", "Token inválido: usuário não está logado")
            return false
        }

        val tokenSavedAt = encryptedPrefs.getLong("token_saved_at", 0)
        val expiresIn = encryptedPrefs.getLong("expires_in", 0)

        if (tokenSavedAt == 0L || expiresIn == 0L) {
            logger.warning("TokenManager", "Token inválido: dados de expiração ausentes")
            return false
        }

        val currentTime = System.currentTimeMillis()
        val tokenExpirationTime = tokenSavedAt + (expiresIn * 1000) // expiresIn em segundos

        // Considerar token inválido se expira em menos de 5 minutos
        val bufferTime = 5 * 60 * 1000 // 5 minutos em millisegundos
        val isValid = currentTime < (tokenExpirationTime - bufferTime)

        if (!isValid) {
            logger.auth("Token expirado detectado")
        } else {
            val timeToExpire = (tokenExpirationTime - currentTime) / 1000 / 60 // minutos
            logger.debug("TokenManager", "Token válido, expira em $timeToExpire minutos")
        }

        return isValid
    }
    
    /**
     * Limpa todos os tokens e dados do usuário
     */
    override suspend fun clearTokens() {
        val currentUsername = _currentUser.value?.username
        logger.auth("Logout realizado para usuário: ${currentUsername ?: "desconhecido"}")

        encryptedPrefs.edit().clear().apply()
        _currentUser.value = null
        _isAuthenticated.value = false

        logger.info("TokenManager", "Tokens limpos com sucesso")
    }
    
    /**
     * Atualiza dados do usuário atual
     */
    override suspend fun updateUser(user: UserLogin) {
        if (_isAuthenticated.value) {
            encryptedPrefs.edit().apply {
                putLong("user_id", user.id)
                putString("user_username", user.username)
                putString("user_email", user.email)
                putString("user_full_name", user.fullName)
                putString("user_avatar", user.avatar)
                putBoolean("user_is_active", user.isActive)
                putString("user_created_at", user.createdAt)
                putString("user_updated_at", user.updatedAt)
                apply()
            }
            _currentUser.value = user
        }
    }
    
    /**
     * Verifica e restaura o estado de autenticação na inicialização
     */
    private fun checkAuthenticationStatus() {
        try {
            logger.info("TokenManager", "Verificando status de autenticação")
            val isLoggedIn = encryptedPrefs.getBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)

            if (isLoggedIn) {
                // Restaurar dados do usuário
                val userId = encryptedPrefs.getLong("user_id", 0)
                val username = encryptedPrefs.getString("user_username", "") ?: ""
                val email = encryptedPrefs.getString("user_email", "") ?: ""
                val fullName = encryptedPrefs.getString("user_full_name", "") ?: ""
                val avatar = encryptedPrefs.getString("user_avatar", null)
                val isActive = encryptedPrefs.getBoolean("user_is_active", true)
                val createdAt = encryptedPrefs.getString("user_created_at", "") ?: ""
                val updatedAt = encryptedPrefs.getString("user_updated_at", "") ?: ""

                if (userId > 0 && username.isNotEmpty() && email.isNotEmpty()) {
                    val user = UserLogin(
                        id = userId,
                        username = username,
                        email = email,
                        fullName = fullName,
                        avatar = avatar,
                        isActive = isActive,
                        createdAt = createdAt,
                        updatedAt = updatedAt
                    )

                    _currentUser.value = user
                    _isAuthenticated.value = true
                    logger.auth("Verificação de autenticação: autenticado")
                    logger.info("TokenManager", "Usuário restaurado: $username")
                } else {
                    logger.warning("TokenManager", "Dados de usuário incompletos encontrados")
                    _isAuthenticated.value = false
                    _currentUser.value = null
                }
            } else {
                logger.auth("Verificação de autenticação: não autenticado")
            }
        } catch (e: Exception) {
            logger.error("TokenManager", "Erro ao verificar status de autenticação", e)
            // Em caso de erro, limpar estado
            _isAuthenticated.value = false
            _currentUser.value = null
        }
    }

    /**
     * Obtém informações de expiração do token para cálculos externos
     */
    override suspend fun getTokenExpirationInfo(): Pair<Long, Long>? {
        return try {
            val tokenSavedAt = encryptedPrefs.getLong("token_saved_at", 0)
            val expiresIn = encryptedPrefs.getLong("expires_in", 0)

            if (tokenSavedAt == 0L || expiresIn == 0L) {
                null
            } else {
                Pair(tokenSavedAt, expiresIn)
            }
        } catch (e: Exception) {
            logger.error("TokenManager", "Erro ao obter informações de expiração do token", e)
            null
        }
    }
}
