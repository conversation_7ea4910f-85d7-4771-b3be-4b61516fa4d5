#androidx.activity.ComponentActivity.com.example.myapplication.core.common.Resource0com.example.myapplication.core.navigation.Screenokhttp3.InterceptorEcom.example.myapplication.core.security.IAuthenticationManagerService<com.example.myapplication.core.security.ITokenManagerService:com.example.myapplication.domain.repository.AuthRepositoryFcom.example.myapplication.features.auth.presentation.login.LoginResult-com.example.myapplication.core.common.UiEventEcom.example.myapplication.features.auth.presentation.login.LoginEventandroidx.lifecycle.ViewModel,androidx.lifecycle.ViewModelProvider.FactoryLcom.example.myapplication.features.auth.presentation.register.RegisterResultKcom.example.myapplication.features.auth.presentation.register.RegisterEvent                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            