package com.example.myapplication.features.profile.domain.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.profile.domain.model.ProfileResponse

/**
 * Interface do repositório de perfil
 * 
 * Seguindo DIP: Abstração que define contratos para operações de perfil
 * Seguindo ISP: Interface específica para operações de perfil
 */
interface ProfileRepository {
    
    /**
     * Obtém o perfil completo do usuário autenticado
     * @return Resource contendo dados do perfil ou erro
     */
    suspend fun getProfile(): Resource<ProfileResponse>
    
    /**
     * Atualiza dados do perfil do usuário
     * @param profileData Dados atualizados do perfil
     * @return Resource indicando sucesso ou erro
     */
    suspend fun updateProfile(profileData: Map<String, Any>): Resource<ProfileResponse>
    
    /**
     * Atualiza avatar do usuário
     * @param avatarUrl URL do novo avatar
     * @return Resource indicando sucesso ou erro
     */
    suspend fun updateAvatar(avatarUrl: String): Resource<ProfileResponse>
}
