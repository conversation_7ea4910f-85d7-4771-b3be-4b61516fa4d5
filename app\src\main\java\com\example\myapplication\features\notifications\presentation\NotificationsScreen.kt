package com.example.myapplication.features.notifications.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Book
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Tela de notificações da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de exibir notificações
 */
@Composable
fun NotificationsScreen(
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            Text(
                text = "Notificações",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        item {
            NotificationSection(
                title = "Hoje",
                notifications = getTodayNotifications()
            )
        }
        
        item {
            NotificationSection(
                title = "Esta Semana",
                notifications = getWeekNotifications()
            )
        }
        
        item {
            NotificationSection(
                title = "Anteriores",
                notifications = getOlderNotifications()
            )
        }
    }
}

@Composable
private fun NotificationSection(
    title: String,
    notifications: List<NotificationItem>
) {
    if (notifications.isEmpty()) return
    
    Column {
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        notifications.forEach { notification ->
            NotificationCard(notification = notification)
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

@Composable
private fun NotificationCard(notification: NotificationItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (notification.isRead) 
                MaterialTheme.colorScheme.surface 
            else 
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // Ícone da notificação
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(notification.color.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = notification.icon,
                    contentDescription = null,
                    tint = notification.color,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Conteúdo da notificação
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = notification.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = notification.message,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    lineHeight = 20.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = notification.time,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
            
            // Indicador de não lida
            if (!notification.isRead) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary)
                )
            }
        }
    }
}

// Data class
data class NotificationItem(
    val id: String,
    val title: String,
    val message: String,
    val time: String,
    val icon: ImageVector,
    val color: Color,
    val isRead: Boolean = false,
    val type: NotificationType
)

enum class NotificationType {
    BOOK_RECOMMENDATION,
    READING_PROGRESS,
    SOCIAL,
    ACHIEVEMENT,
    SYSTEM
}

// Dados mockados
private fun getTodayNotifications() = listOf(
    NotificationItem(
        id = "1",
        title = "Nova Recomendação",
        message = "Baseado em suas leituras recentes, recomendamos 'Kotlin in Action'. Confira agora!",
        time = "2 horas atrás",
        icon = Icons.Default.Book,
        color = Color(0xFF4CAF50),
        isRead = false,
        type = NotificationType.BOOK_RECOMMENDATION
    ),
    NotificationItem(
        id = "2",
        title = "Meta de Leitura",
        message = "Parabéns! Você atingiu 80% da sua meta mensal de leitura.",
        time = "4 horas atrás",
        icon = Icons.Default.TrendingUp,
        color = Color(0xFF2196F3),
        isRead = false,
        type = NotificationType.READING_PROGRESS
    ),
    NotificationItem(
        id = "3",
        title = "Novo Seguidor",
        message = "Ana Silva começou a seguir você. Veja o perfil dela!",
        time = "6 horas atrás",
        icon = Icons.Default.Group,
        color = Color(0xFF9C27B0),
        isRead = true,
        type = NotificationType.SOCIAL
    )
)

private fun getWeekNotifications() = listOf(
    NotificationItem(
        id = "4",
        title = "Conquista Desbloqueada",
        message = "Você desbloqueou a conquista 'Leitor Dedicado' por ler 5 livros este mês!",
        time = "2 dias atrás",
        icon = Icons.Default.Star,
        color = Color(0xFFFF9800),
        isRead = true,
        type = NotificationType.ACHIEVEMENT
    ),
    NotificationItem(
        id = "5",
        title = "Livro Favoritado",
        message = "João Santos favoritou o mesmo livro que você: 'Clean Architecture'",
        time = "3 dias atrás",
        icon = Icons.Default.Favorite,
        color = Color(0xFFE91E63),
        isRead = true,
        type = NotificationType.SOCIAL
    ),
    NotificationItem(
        id = "6",
        title = "Lembrete de Leitura",
        message = "Que tal continuar lendo 'Design Patterns'? Você parou na página 127.",
        time = "5 dias atrás",
        icon = Icons.Default.Book,
        color = Color(0xFF4CAF50),
        isRead = true,
        type = NotificationType.READING_PROGRESS
    )
)

private fun getOlderNotifications() = listOf(
    NotificationItem(
        id = "7",
        title = "Atualização do App",
        message = "Nova versão disponível com melhorias de performance e correções de bugs.",
        time = "1 semana atrás",
        icon = Icons.Default.TrendingUp,
        color = Color(0xFF607D8B),
        isRead = true,
        type = NotificationType.SYSTEM
    ),
    NotificationItem(
        id = "8",
        title = "Avaliação Pendente",
        message = "Você terminou de ler 'Effective Java'. Que tal avaliar o livro?",
        time = "2 semanas atrás",
        icon = Icons.Default.Star,
        color = Color(0xFFFF9800),
        isRead = true,
        type = NotificationType.READING_PROGRESS
    )
)

@Preview(showBackground = true)
@Composable
fun NotificationsScreenPreview() {
    MyApplicationTheme {
        NotificationsScreen()
    }
}
