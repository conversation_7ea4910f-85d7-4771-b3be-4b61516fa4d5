package com.example.myapplication.features.auth.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.security.TokenManager
import com.example.myapplication.domain.model.AuthToken
import com.example.myapplication.domain.repository.AuthRepository

/**
 * Caso de uso para refresh de tokens de autenticação
 * 
 * Seguindo SRP: Responsabilidade única de renovar tokens expirados
 * Seguindo DIP: Depende de abstrações (AuthRepository, TokenManager)
 * 
 * Funcionalidades:
 * - Verifica se refresh é necessário
 * - Utiliza refresh token para obter novos tokens
 * - Atualiza armazenamento seguro automaticamente
 * - Trata erros de refresh (token inválido, expirado, etc.)
 */
class RefreshTokenUseCase(
    private val authRepository: AuthRepository,
    private val tokenManager: TokenManager
) {
    
    /**
     * Executa refresh de token se necessário
     * 
     * @return Boolean indicando sucesso do refresh
     */
    suspend operator fun invoke(): <PERSON><PERSON><PERSON> {
        return try {
            // Verificar se já temos um token válido
            if (tokenManager.isTokenValid()) {
                return true
            }
            
            // Obter refresh token
            val refreshToken = tokenManager.getRefreshToken()
            if (refreshToken.isNullOrEmpty()) {
                // Sem refresh token, usuário precisa fazer login novamente
                tokenManager.clearTokens()
                return false
            }
            
            // Tentar refresh
            val result = authRepository.refreshToken(refreshToken)
            
            when (result) {
                is Resource.Success -> {
                    val authResponse = result.data!!
                    
                    // Criar AuthToken com novos dados
                    val newAuthToken = AuthToken(
                        accessToken = authResponse.accessToken,
                        refreshToken = authResponse.refreshToken,
                        tokenType = authResponse.tokenType,
                        expiresIn = authResponse.expiresIn
                    )
                    
                    // Manter usuário atual e atualizar apenas tokens
                    val currentUser = tokenManager.currentUser.value
                    if (currentUser != null) {
                        tokenManager.saveTokens(newAuthToken, currentUser)
                        true
                    } else {
                        // Usuário não encontrado, limpar tokens
                        tokenManager.clearTokens()
                        false
                    }
                }
                
                is Resource.Error -> {
                    // Refresh falhou, limpar tokens para forçar novo login
                    tokenManager.clearTokens()
                    false
                }
                
                is Resource.Loading -> {
                    // Não deveria acontecer em suspend function
                    false
                }
            }
            
        } catch (e: Exception) {
            // Em caso de erro, limpar tokens
            tokenManager.clearTokens()
            false
        }
    }
    
    /**
     * Verifica se refresh é necessário baseado na validade do token
     */
    suspend fun isRefreshNeeded(): Boolean {
        return !tokenManager.isTokenValid() && !tokenManager.getRefreshToken().isNullOrEmpty()
    }
    
    /**
     * Força refresh mesmo com token válido (útil para testes ou situações específicas)
     */
    suspend fun forceRefresh(): Resource<Boolean> {
        return try {
            val refreshToken = tokenManager.getRefreshToken()
            if (refreshToken.isNullOrEmpty()) {
                return Resource.Error("Refresh token não encontrado")
            }
            
            val result = authRepository.refreshToken(refreshToken)
            
            when (result) {
                is Resource.Success -> {
                    val authResponse = result.data!!
                    val newAuthToken = AuthToken(
                        accessToken = authResponse.accessToken,
                        refreshToken = authResponse.refreshToken,
                        tokenType = authResponse.tokenType,
                        expiresIn = authResponse.expiresIn
                    )
                    
                    val currentUser = tokenManager.currentUser.value
                    if (currentUser != null) {
                        tokenManager.saveTokens(newAuthToken, currentUser)
                        Resource.Success(true)
                    } else {
                        Resource.Error("Usuário não encontrado")
                    }
                }
                
                is Resource.Error -> {
                    Resource.Error(result.message ?: "Erro ao renovar token")
                }
                
                is Resource.Loading -> {
                    Resource.Loading()
                }
            }
            
        } catch (e: Exception) {
            Resource.Error("Erro inesperado ao renovar token: ${e.message}")
        }
    }
}
