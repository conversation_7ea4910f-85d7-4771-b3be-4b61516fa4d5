  ic_dialog_alert android.R.drawable  ic_dialog_info android.R.drawable  
AppNavigation android.app.Activity  Modifier android.app.Activity  MyApplicationTheme android.app.Activity  Scaffold android.app.Activity  Toast android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Context android.content  SharedPreferences android.content  
AppNavigation android.content.Context  Modifier android.content.Context  MyApplicationTheme android.content.Context  Scaffold android.content.Context  Toast android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  
AppNavigation android.content.ContextWrapper  Modifier android.content.ContextWrapper  MyApplicationTheme android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Toast android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  Bundle 
android.os  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  wtf android.util.Log  
EMAIL_ADDRESS android.util.Patterns  
AppNavigation  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  MyApplicationTheme  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_LONG android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
AppNavigation #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  MyApplicationTheme #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  Easing androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  animateFloat androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  tween androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Reverse *androidx.compose.animation.core.RepeatMode  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  AppConstants "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FocusDirection "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  HttpURLConnection "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  
LoginEvent "androidx.compose.foundation.layout  LoginResult "androidx.compose.foundation.layout  LoginViewModel "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  
RegisterEvent "androidx.compose.foundation.layout  RegisterResult "androidx.compose.foundation.layout  RegisterViewModel "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  VisualTransformation "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  
mutableListOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  testConnectivity "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  alpha +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  AppConstants .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  FocusDirection .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LoginEvent .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  
RegisterEvent .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  System .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  
Visibility .androidx.compose.foundation.layout.ColumnScope  
VisibilityOff .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  alpha .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  testConnectivity .androidx.compose.foundation.layout.ColumnScope  Error .androidx.compose.foundation.layout.LoginResult  Success .androidx.compose.foundation.layout.LoginResult  Error 1androidx.compose.foundation.layout.RegisterResult  Success 1androidx.compose.foundation.layout.RegisterResult  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  FocusDirection 4androidx.compose.foundation.text.KeyboardActionScope  
LoginEvent 4androidx.compose.foundation.text.KeyboardActionScope  
RegisterEvent 4androidx.compose.foundation.text.KeyboardActionScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  
Visibility ,androidx.compose.material.icons.Icons.Filled  
VisibilityOff ,androidx.compose.material.icons.Icons.Filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  AppConstants androidx.compose.material3  Arrangement androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FocusDirection androidx.compose.material3  
FontWeight androidx.compose.material3  HttpURLConnection androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  	ImeAction androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  List androidx.compose.material3  
LoginEvent androidx.compose.material3  LoginResult androidx.compose.material3  LoginViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Preview androidx.compose.material3  
RegisterEvent androidx.compose.material3  RegisterResult androidx.compose.material3  RegisterViewModel androidx.compose.material3  Scaffold androidx.compose.material3  Shapes androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  VisualTransformation androidx.compose.material3  
cardColors androidx.compose.material3  darkColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  
mutableListOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  testConnectivity androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  Error &androidx.compose.material3.LoginResult  Success &androidx.compose.material3.LoginResult  colorScheme (androidx.compose.material3.MaterialTheme  shapes (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  Error )androidx.compose.material3.RegisterResult  Success )androidx.compose.material3.RegisterResult  	Alignment androidx.compose.runtime  AppConstants androidx.compose.runtime  Arrangement androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FocusDirection androidx.compose.runtime  
FontWeight androidx.compose.runtime  HttpURLConnection androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  	ImeAction androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  List androidx.compose.runtime  
LoginEvent androidx.compose.runtime  LoginResult androidx.compose.runtime  LoginViewModel androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
RegisterEvent androidx.compose.runtime  RegisterResult androidx.compose.runtime  RegisterViewModel androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  Unit androidx.compose.runtime  VisualTransformation androidx.compose.runtime  
cardColors androidx.compose.runtime  collectAsState androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  
mutableListOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  testConnectivity androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  Error $androidx.compose.runtime.LoginResult  Success $androidx.compose.runtime.LoginResult  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  Error 'androidx.compose.runtime.RegisterResult  Success 'androidx.compose.runtime.RegisterResult  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  alpha androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  alpha &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  alpha androidx.compose.ui.draw  FocusDirection androidx.compose.ui.focus  FocusManager androidx.compose.ui.focus  	Companion (androidx.compose.ui.focus.FocusDirection  Down (androidx.compose.ui.focus.FocusDirection  Down 2androidx.compose.ui.focus.FocusDirection.Companion  
clearFocus &androidx.compose.ui.focus.FocusManager  	moveFocus &androidx.compose.ui.focus.FocusManager  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalFocusManager androidx.compose.ui.platform  painterResource androidx.compose.ui.res  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Next (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  Next 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Text +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  Text 5androidx.compose.ui.text.input.KeyboardType.Companion  	Companion 3androidx.compose.ui.text.input.VisualTransformation  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AppNavigation #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  MyApplicationTheme #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  viewModelScope androidx.lifecycle  Factory $androidx.lifecycle.ViewModelProvider  collectAsStateWithLifecycle androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  EncryptedSharedPreferences androidx.security.crypto  	MasterKey androidx.security.crypto  PrefKeyEncryptionScheme 3androidx.security.crypto.EncryptedSharedPreferences  PrefValueEncryptionScheme 3androidx.security.crypto.EncryptedSharedPreferences  create 3androidx.security.crypto.EncryptedSharedPreferences  
AES256_SIV Kandroidx.security.crypto.EncryptedSharedPreferences.PrefKeyEncryptionScheme  
AES256_GCM Mandroidx.security.crypto.EncryptedSharedPreferences.PrefValueEncryptionScheme  Builder "androidx.security.crypto.MasterKey  	KeyScheme "androidx.security.crypto.MasterKey  build *androidx.security.crypto.MasterKey.Builder  setKeyScheme *androidx.security.crypto.MasterKey.Builder  
AES256_GCM ,androidx.security.crypto.MasterKey.KeyScheme  
AppNavigation com.example.myapplication  Bundle com.example.myapplication  ComponentActivity com.example.myapplication  MainActivity com.example.myapplication  Modifier com.example.myapplication  MyApplicationTheme com.example.myapplication  R com.example.myapplication  Scaffold com.example.myapplication  Toast com.example.myapplication  fillMaxSize com.example.myapplication  padding com.example.myapplication  
AppNavigation &com.example.myapplication.MainActivity  Modifier &com.example.myapplication.MainActivity  MyApplicationTheme &com.example.myapplication.MainActivity  Scaffold &com.example.myapplication.MainActivity  Toast &com.example.myapplication.MainActivity  enableEdgeToEdge &com.example.myapplication.MainActivity  fillMaxSize &com.example.myapplication.MainActivity  padding &com.example.myapplication.MainActivity  
setContent &com.example.myapplication.MainActivity  Boolean %com.example.myapplication.core.common  Resource %com.example.myapplication.core.common  String %com.example.myapplication.core.common  T %com.example.myapplication.core.common  UiEffect %com.example.myapplication.core.common  UiEvent %com.example.myapplication.core.common  UiState %com.example.myapplication.core.common  Error .com.example.myapplication.core.common.Resource  Loading .com.example.myapplication.core.common.Resource  Resource .com.example.myapplication.core.common.Resource  String .com.example.myapplication.core.common.Resource  Success .com.example.myapplication.core.common.Resource  T .com.example.myapplication.core.common.Resource  data .com.example.myapplication.core.common.Resource  message .com.example.myapplication.core.common.Resource  message 4com.example.myapplication.core.common.Resource.Error  data 6com.example.myapplication.core.common.Resource.Success  AppConstants (com.example.myapplication.core.constants  Api 5com.example.myapplication.core.constants.AppConstants  Preferences 5com.example.myapplication.core.constants.AppConstants  
Validation 5com.example.myapplication.core.constants.AppConstants  BASE_URL 9com.example.myapplication.core.constants.AppConstants.Api  TIMEOUT 9com.example.myapplication.core.constants.AppConstants.Api  ACCESS_TOKEN Acom.example.myapplication.core.constants.AppConstants.Preferences  IS_LOGGED_IN Acom.example.myapplication.core.constants.AppConstants.Preferences  
REFRESH_TOKEN Acom.example.myapplication.core.constants.AppConstants.Preferences  USER_PREFERENCES Acom.example.myapplication.core.constants.AppConstants.Preferences  MIN_PASSWORD_LENGTH @com.example.myapplication.core.constants.AppConstants.Validation  MIN_USERNAME_LENGTH @com.example.myapplication.core.constants.AppConstants.Validation  
ApiService !com.example.myapplication.core.di  AppConstants !com.example.myapplication.core.di  AppContainer !com.example.myapplication.core.di  AuthInterceptor !com.example.myapplication.core.di  AuthRepository !com.example.myapplication.core.di  AuthRepositoryImpl !com.example.myapplication.core.di  AuthenticationManager !com.example.myapplication.core.di  AuthenticationManagerFactory !com.example.myapplication.core.di  Context !com.example.myapplication.core.di  GsonConverterFactory !com.example.myapplication.core.di  HttpLoggingInterceptor !com.example.myapplication.core.di  LoginUseCase !com.example.myapplication.core.di  
LogoutUseCase !com.example.myapplication.core.di  OkHttpClient !com.example.myapplication.core.di  RefreshTokenUseCase !com.example.myapplication.core.di  RegisterUseCase !com.example.myapplication.core.di  Retrofit !com.example.myapplication.core.di  TimeUnit !com.example.myapplication.core.di  TokenManager !com.example.myapplication.core.di  TokenRefreshService !com.example.myapplication.core.di  TokenRefreshServiceFactory !com.example.myapplication.core.di  apply !com.example.myapplication.core.di  create !com.example.myapplication.core.di  getInstance !com.example.myapplication.core.di  getValue !com.example.myapplication.core.di  java !com.example.myapplication.core.di  lazy !com.example.myapplication.core.di  provideDelegate !com.example.myapplication.core.di  
ApiService .com.example.myapplication.core.di.AppContainer  AppConstants .com.example.myapplication.core.di.AppContainer  AuthInterceptor .com.example.myapplication.core.di.AppContainer  AuthRepositoryImpl .com.example.myapplication.core.di.AppContainer  AuthenticationManagerFactory .com.example.myapplication.core.di.AppContainer  GsonConverterFactory .com.example.myapplication.core.di.AppContainer  HttpLoggingInterceptor .com.example.myapplication.core.di.AppContainer  LoginUseCase .com.example.myapplication.core.di.AppContainer  
LogoutUseCase .com.example.myapplication.core.di.AppContainer  OkHttpClient .com.example.myapplication.core.di.AppContainer  RefreshTokenUseCase .com.example.myapplication.core.di.AppContainer  RegisterUseCase .com.example.myapplication.core.di.AppContainer  Retrofit .com.example.myapplication.core.di.AppContainer  TimeUnit .com.example.myapplication.core.di.AppContainer  TokenManager .com.example.myapplication.core.di.AppContainer  TokenRefreshServiceFactory .com.example.myapplication.core.di.AppContainer  
apiService .com.example.myapplication.core.di.AppContainer  apply .com.example.myapplication.core.di.AppContainer  authInterceptor .com.example.myapplication.core.di.AppContainer  authRepository .com.example.myapplication.core.di.AppContainer  authenticationManager .com.example.myapplication.core.di.AppContainer  context .com.example.myapplication.core.di.AppContainer  create .com.example.myapplication.core.di.AppContainer  getInstance .com.example.myapplication.core.di.AppContainer  getValue .com.example.myapplication.core.di.AppContainer  java .com.example.myapplication.core.di.AppContainer  lazy .com.example.myapplication.core.di.AppContainer  loginUseCase .com.example.myapplication.core.di.AppContainer  
logoutUseCase .com.example.myapplication.core.di.AppContainer  okHttpClient .com.example.myapplication.core.di.AppContainer  provideDelegate .com.example.myapplication.core.di.AppContainer  refreshTokenUseCase .com.example.myapplication.core.di.AppContainer  registerUseCase .com.example.myapplication.core.di.AppContainer  retrofit .com.example.myapplication.core.di.AppContainer  tokenManager .com.example.myapplication.core.di.AppContainer  tokenRefreshService .com.example.myapplication.core.di.AppContainer  Any &com.example.myapplication.core.logging  	AppLogger &com.example.myapplication.core.logging  Boolean &com.example.myapplication.core.logging  CRITICAL &com.example.myapplication.core.logging  DEBUG &com.example.myapplication.core.logging  Date &com.example.myapplication.core.logging  ERROR &com.example.myapplication.core.logging  ILoggerService &com.example.myapplication.core.logging  INFO &com.example.myapplication.core.logging  Int &com.example.myapplication.core.logging  Locale &com.example.myapplication.core.logging  Log &com.example.myapplication.core.logging  	LogConfig &com.example.myapplication.core.logging  LogLevel &com.example.myapplication.core.logging  Long &com.example.myapplication.core.logging  SimpleDateFormat &com.example.myapplication.core.logging  String &com.example.myapplication.core.logging  TAG_AUTH &com.example.myapplication.core.logging  TAG_NETWORK &com.example.myapplication.core.logging  TAG_SECURITY &com.example.myapplication.core.logging  	Throwable &com.example.myapplication.core.logging  Volatile &com.example.myapplication.core.logging  WARNING &com.example.myapplication.core.logging  also &com.example.myapplication.core.logging  getInstance &com.example.myapplication.core.logging  java &com.example.myapplication.core.logging  logCritical &com.example.myapplication.core.logging  logDebug &com.example.myapplication.core.logging  logError &com.example.myapplication.core.logging  logInfo &com.example.myapplication.core.logging  
logWarning &com.example.myapplication.core.logging  synchronized &com.example.myapplication.core.logging  	uppercase &com.example.myapplication.core.logging  	AppLogger 0com.example.myapplication.core.logging.AppLogger  Boolean 0com.example.myapplication.core.logging.AppLogger  	Companion 0com.example.myapplication.core.logging.AppLogger  Date 0com.example.myapplication.core.logging.AppLogger  INSTANCE 0com.example.myapplication.core.logging.AppLogger  Int 0com.example.myapplication.core.logging.AppLogger  Locale 0com.example.myapplication.core.logging.AppLogger  Log 0com.example.myapplication.core.logging.AppLogger  	LogConfig 0com.example.myapplication.core.logging.AppLogger  LogLevel 0com.example.myapplication.core.logging.AppLogger  Long 0com.example.myapplication.core.logging.AppLogger  SimpleDateFormat 0com.example.myapplication.core.logging.AppLogger  String 0com.example.myapplication.core.logging.AppLogger  TAG_AUTH 0com.example.myapplication.core.logging.AppLogger  TAG_NETWORK 0com.example.myapplication.core.logging.AppLogger  TAG_SECURITY 0com.example.myapplication.core.logging.AppLogger  	Throwable 0com.example.myapplication.core.logging.AppLogger  Volatile 0com.example.myapplication.core.logging.AppLogger  also 0com.example.myapplication.core.logging.AppLogger  auth 0com.example.myapplication.core.logging.AppLogger  config 0com.example.myapplication.core.logging.AppLogger  critical 0com.example.myapplication.core.logging.AppLogger  
dateFormat 0com.example.myapplication.core.logging.AppLogger  debug 0com.example.myapplication.core.logging.AppLogger  error 0com.example.myapplication.core.logging.AppLogger  getInstance 0com.example.myapplication.core.logging.AppLogger  info 0com.example.myapplication.core.logging.AppLogger  logToConsole 0com.example.myapplication.core.logging.AppLogger  network 0com.example.myapplication.core.logging.AppLogger  security 0com.example.myapplication.core.logging.AppLogger  	shouldLog 0com.example.myapplication.core.logging.AppLogger  synchronized 0com.example.myapplication.core.logging.AppLogger  warning 0com.example.myapplication.core.logging.AppLogger  	AppLogger :com.example.myapplication.core.logging.AppLogger.Companion  Date :com.example.myapplication.core.logging.AppLogger.Companion  INSTANCE :com.example.myapplication.core.logging.AppLogger.Companion  Locale :com.example.myapplication.core.logging.AppLogger.Companion  Log :com.example.myapplication.core.logging.AppLogger.Companion  	LogConfig :com.example.myapplication.core.logging.AppLogger.Companion  LogLevel :com.example.myapplication.core.logging.AppLogger.Companion  SimpleDateFormat :com.example.myapplication.core.logging.AppLogger.Companion  TAG_AUTH :com.example.myapplication.core.logging.AppLogger.Companion  TAG_NETWORK :com.example.myapplication.core.logging.AppLogger.Companion  TAG_SECURITY :com.example.myapplication.core.logging.AppLogger.Companion  also :com.example.myapplication.core.logging.AppLogger.Companion  getInstance :com.example.myapplication.core.logging.AppLogger.Companion  synchronized :com.example.myapplication.core.logging.AppLogger.Companion  enableConsoleLogging 0com.example.myapplication.core.logging.LogConfig  minLevel 0com.example.myapplication.core.logging.LogConfig  CRITICAL /com.example.myapplication.core.logging.LogLevel  	Companion /com.example.myapplication.core.logging.LogLevel  DEBUG /com.example.myapplication.core.logging.LogLevel  ERROR /com.example.myapplication.core.logging.LogLevel  INFO /com.example.myapplication.core.logging.LogLevel  Int /com.example.myapplication.core.logging.LogLevel  LogLevel /com.example.myapplication.core.logging.LogLevel  String /com.example.myapplication.core.logging.LogLevel  WARNING /com.example.myapplication.core.logging.LogLevel  priority /com.example.myapplication.core.logging.LogLevel  	uppercase /com.example.myapplication.core.logging.LogLevel  CRITICAL 9com.example.myapplication.core.logging.LogLevel.Companion  DEBUG 9com.example.myapplication.core.logging.LogLevel.Companion  ERROR 9com.example.myapplication.core.logging.LogLevel.Companion  INFO 9com.example.myapplication.core.logging.LogLevel.Companion  WARNING 9com.example.myapplication.core.logging.LogLevel.Companion  	uppercase 9com.example.myapplication.core.logging.LogLevel.Companion  
AppNavigation )com.example.myapplication.core.navigation  
Composable )com.example.myapplication.core.navigation  	Exception )com.example.myapplication.core.navigation  LoginResult )com.example.myapplication.core.navigation  LoginViewModel )com.example.myapplication.core.navigation  Modifier )com.example.myapplication.core.navigation  NavigationGraph )com.example.myapplication.core.navigation  RegisterResult )com.example.myapplication.core.navigation  RegisterViewModel )com.example.myapplication.core.navigation  Screen )com.example.myapplication.core.navigation  String )com.example.myapplication.core.navigation  Unit )com.example.myapplication.core.navigation  kotlinx )com.example.myapplication.core.navigation  provideDelegate )com.example.myapplication.core.navigation  Success 5com.example.myapplication.core.navigation.LoginResult  Success 8com.example.myapplication.core.navigation.RegisterResult  Debug 0com.example.myapplication.core.navigation.Screen  Login 0com.example.myapplication.core.navigation.Screen  Main 0com.example.myapplication.core.navigation.Screen  NetworkTest 0com.example.myapplication.core.navigation.Screen  Register 0com.example.myapplication.core.navigation.Screen  Screen 0com.example.myapplication.core.navigation.Screen  Splash 0com.example.myapplication.core.navigation.Screen  String 0com.example.myapplication.core.navigation.Screen  AuthInterceptor &com.example.myapplication.core.network  AuthInterceptorFactory &com.example.myapplication.core.network  AuthInterceptorWithRefresh &com.example.myapplication.core.network  Boolean &com.example.myapplication.core.network  	Exception &com.example.myapplication.core.network  IOException &com.example.myapplication.core.network  Interceptor &com.example.myapplication.core.network  OPEN_ENDPOINTS &com.example.myapplication.core.network  Response &com.example.myapplication.core.network  Throws &com.example.myapplication.core.network  TokenManager &com.example.myapplication.core.network  contains &com.example.myapplication.core.network  
isNullOrEmpty &com.example.myapplication.core.network  listOf &com.example.myapplication.core.network  none &com.example.myapplication.core.network  okhttp3 &com.example.myapplication.core.network  refreshTokenUseCase &com.example.myapplication.core.network  runBlocking &com.example.myapplication.core.network  tokenManager &com.example.myapplication.core.network  	Exception 6com.example.myapplication.core.network.AuthInterceptor  IOException 6com.example.myapplication.core.network.AuthInterceptor  Interceptor 6com.example.myapplication.core.network.AuthInterceptor  OPEN_ENDPOINTS 6com.example.myapplication.core.network.AuthInterceptor  Response 6com.example.myapplication.core.network.AuthInterceptor  Throws 6com.example.myapplication.core.network.AuthInterceptor  TokenManager 6com.example.myapplication.core.network.AuthInterceptor  contains 6com.example.myapplication.core.network.AuthInterceptor  
isNullOrEmpty 6com.example.myapplication.core.network.AuthInterceptor  listOf 6com.example.myapplication.core.network.AuthInterceptor  none 6com.example.myapplication.core.network.AuthInterceptor  runBlocking 6com.example.myapplication.core.network.AuthInterceptor  tokenManager 6com.example.myapplication.core.network.AuthInterceptor  IOException @com.example.myapplication.core.network.AuthInterceptor.Companion  OPEN_ENDPOINTS @com.example.myapplication.core.network.AuthInterceptor.Companion  contains @com.example.myapplication.core.network.AuthInterceptor.Companion  
isNullOrEmpty @com.example.myapplication.core.network.AuthInterceptor.Companion  listOf @com.example.myapplication.core.network.AuthInterceptor.Companion  none @com.example.myapplication.core.network.AuthInterceptor.Companion  runBlocking @com.example.myapplication.core.network.AuthInterceptor.Companion  tokenManager @com.example.myapplication.core.network.AuthInterceptor.Companion  Chain Bcom.example.myapplication.core.network.AuthInterceptor.Interceptor  AuthInterceptor =com.example.myapplication.core.network.AuthInterceptorFactory  AuthInterceptorWithRefresh =com.example.myapplication.core.network.AuthInterceptorFactory  Boolean Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  	Exception Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  IOException Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  Interceptor Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  OPEN_ENDPOINTS Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  Response Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  Throws Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  TokenManager Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  contains Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  
isNullOrEmpty Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  listOf Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  none Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  okhttp3 Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  proceedWithAuth Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  refreshTokenUseCase Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  runBlocking Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  tokenManager Acom.example.myapplication.core.network.AuthInterceptorWithRefresh  IOException Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  OPEN_ENDPOINTS Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  contains Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  
isNullOrEmpty Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  listOf Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  none Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  refreshTokenUseCase Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  runBlocking Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  tokenManager Kcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Companion  Chain Mcom.example.myapplication.core.network.AuthInterceptorWithRefresh.Interceptor  Request Icom.example.myapplication.core.network.AuthInterceptorWithRefresh.okhttp3  Chain 2com.example.myapplication.core.network.Interceptor  Request .com.example.myapplication.core.network.okhttp3  AppConstants 'com.example.myapplication.core.security  	AppLogger 'com.example.myapplication.core.security  	AuthToken 'com.example.myapplication.core.security  AuthenticationManager 'com.example.myapplication.core.security  AuthenticationManagerFactory 'com.example.myapplication.core.security  Boolean 'com.example.myapplication.core.security  CHECK_INTERVAL_MINUTES 'com.example.myapplication.core.security  Context 'com.example.myapplication.core.security  CoroutineScope 'com.example.myapplication.core.security  Dispatchers 'com.example.myapplication.core.security  EncryptedSharedPreferences 'com.example.myapplication.core.security  	Exception 'com.example.myapplication.core.security  IAuthenticationManagerService 'com.example.myapplication.core.security  ITokenManagerService 'com.example.myapplication.core.security  ITokenRefreshService 'com.example.myapplication.core.security  Job 'com.example.myapplication.core.security  
LoginResponse 'com.example.myapplication.core.security  
LogoutUseCase 'com.example.myapplication.core.security  Long 'com.example.myapplication.core.security  	MasterKey 'com.example.myapplication.core.security  MutableStateFlow 'com.example.myapplication.core.security  Pair 'com.example.myapplication.core.security  REFRESH_THRESHOLD_MINUTES 'com.example.myapplication.core.security  RefreshTokenUseCase 'com.example.myapplication.core.security  RegisterResponse 'com.example.myapplication.core.security  Resource 'com.example.myapplication.core.security  SecurityException 'com.example.myapplication.core.security  SharedPreferences 'com.example.myapplication.core.security  	StateFlow 'com.example.myapplication.core.security  String 'com.example.myapplication.core.security  
SupervisorJob 'com.example.myapplication.core.security  System 'com.example.myapplication.core.security  TokenManager 'com.example.myapplication.core.security  TokenRefreshService 'com.example.myapplication.core.security  TokenRefreshServiceFactory 'com.example.myapplication.core.security  TokenStatus 'com.example.myapplication.core.security  Unit 'com.example.myapplication.core.security  	UserLogin 'com.example.myapplication.core.security  Volatile 'com.example.myapplication.core.security  also 'com.example.myapplication.core.security  apply 'com.example.myapplication.core.security  asStateFlow 'com.example.myapplication.core.security  checkAndRefreshIfNeeded 'com.example.myapplication.core.security  delay 'com.example.myapplication.core.security  getInstance 'com.example.myapplication.core.security  getValue 'com.example.myapplication.core.security  
isNotEmpty 'com.example.myapplication.core.security  launch 'com.example.myapplication.core.security  lazy 'com.example.myapplication.core.security  logger 'com.example.myapplication.core.security  maxOf 'com.example.myapplication.core.security  provideDelegate 'com.example.myapplication.core.security  synchronized 'com.example.myapplication.core.security  	AppLogger =com.example.myapplication.core.security.AuthenticationManager  	AuthToken =com.example.myapplication.core.security.AuthenticationManager  Resource =com.example.myapplication.core.security.AuthenticationManager  Unit =com.example.myapplication.core.security.AuthenticationManager  checkAuthenticationStatus =com.example.myapplication.core.security.AuthenticationManager  currentUser =com.example.myapplication.core.security.AuthenticationManager  getInstance =com.example.myapplication.core.security.AuthenticationManager  handleLoginSuccess =com.example.myapplication.core.security.AuthenticationManager  handleRegisterSuccess =com.example.myapplication.core.security.AuthenticationManager  isAuthenticated =com.example.myapplication.core.security.AuthenticationManager  logger =com.example.myapplication.core.security.AuthenticationManager  logout =com.example.myapplication.core.security.AuthenticationManager  
logoutUseCase =com.example.myapplication.core.security.AuthenticationManager  refreshTokenIfNeeded =com.example.myapplication.core.security.AuthenticationManager  refreshTokenUseCase =com.example.myapplication.core.security.AuthenticationManager  startAutoRefresh =com.example.myapplication.core.security.AuthenticationManager  stopAutoRefresh =com.example.myapplication.core.security.AuthenticationManager  tokenManager =com.example.myapplication.core.security.AuthenticationManager  tokenRefreshService =com.example.myapplication.core.security.AuthenticationManager  AuthenticationManager Dcom.example.myapplication.core.security.AuthenticationManagerFactory  create Dcom.example.myapplication.core.security.AuthenticationManagerFactory  AppConstants 4com.example.myapplication.core.security.TokenManager  	AppLogger 4com.example.myapplication.core.security.TokenManager  	AuthToken 4com.example.myapplication.core.security.TokenManager  Boolean 4com.example.myapplication.core.security.TokenManager  	Companion 4com.example.myapplication.core.security.TokenManager  Context 4com.example.myapplication.core.security.TokenManager  EncryptedSharedPreferences 4com.example.myapplication.core.security.TokenManager  	Exception 4com.example.myapplication.core.security.TokenManager  INSTANCE 4com.example.myapplication.core.security.TokenManager  Long 4com.example.myapplication.core.security.TokenManager  	MasterKey 4com.example.myapplication.core.security.TokenManager  MutableStateFlow 4com.example.myapplication.core.security.TokenManager  Pair 4com.example.myapplication.core.security.TokenManager  SecurityException 4com.example.myapplication.core.security.TokenManager  SharedPreferences 4com.example.myapplication.core.security.TokenManager  	StateFlow 4com.example.myapplication.core.security.TokenManager  String 4com.example.myapplication.core.security.TokenManager  System 4com.example.myapplication.core.security.TokenManager  TokenManager 4com.example.myapplication.core.security.TokenManager  	UserLogin 4com.example.myapplication.core.security.TokenManager  Volatile 4com.example.myapplication.core.security.TokenManager  _currentUser 4com.example.myapplication.core.security.TokenManager  _isAuthenticated 4com.example.myapplication.core.security.TokenManager  also 4com.example.myapplication.core.security.TokenManager  apply 4com.example.myapplication.core.security.TokenManager  asStateFlow 4com.example.myapplication.core.security.TokenManager  checkAuthenticationStatus 4com.example.myapplication.core.security.TokenManager  clearTokens 4com.example.myapplication.core.security.TokenManager  context 4com.example.myapplication.core.security.TokenManager  currentUser 4com.example.myapplication.core.security.TokenManager  encryptedPrefs 4com.example.myapplication.core.security.TokenManager  getAccessToken 4com.example.myapplication.core.security.TokenManager  getInstance 4com.example.myapplication.core.security.TokenManager  getRefreshToken 4com.example.myapplication.core.security.TokenManager  getTokenExpirationInfo 4com.example.myapplication.core.security.TokenManager  getValue 4com.example.myapplication.core.security.TokenManager  isAuthenticated 4com.example.myapplication.core.security.TokenManager  
isNotEmpty 4com.example.myapplication.core.security.TokenManager  isTokenValid 4com.example.myapplication.core.security.TokenManager  lazy 4com.example.myapplication.core.security.TokenManager  logger 4com.example.myapplication.core.security.TokenManager  provideDelegate 4com.example.myapplication.core.security.TokenManager  
saveTokens 4com.example.myapplication.core.security.TokenManager  synchronized 4com.example.myapplication.core.security.TokenManager  AppConstants >com.example.myapplication.core.security.TokenManager.Companion  	AppLogger >com.example.myapplication.core.security.TokenManager.Companion  EncryptedSharedPreferences >com.example.myapplication.core.security.TokenManager.Companion  INSTANCE >com.example.myapplication.core.security.TokenManager.Companion  	MasterKey >com.example.myapplication.core.security.TokenManager.Companion  MutableStateFlow >com.example.myapplication.core.security.TokenManager.Companion  Pair >com.example.myapplication.core.security.TokenManager.Companion  SecurityException >com.example.myapplication.core.security.TokenManager.Companion  System >com.example.myapplication.core.security.TokenManager.Companion  TokenManager >com.example.myapplication.core.security.TokenManager.Companion  	UserLogin >com.example.myapplication.core.security.TokenManager.Companion  also >com.example.myapplication.core.security.TokenManager.Companion  apply >com.example.myapplication.core.security.TokenManager.Companion  asStateFlow >com.example.myapplication.core.security.TokenManager.Companion  getInstance >com.example.myapplication.core.security.TokenManager.Companion  getValue >com.example.myapplication.core.security.TokenManager.Companion  
isNotEmpty >com.example.myapplication.core.security.TokenManager.Companion  lazy >com.example.myapplication.core.security.TokenManager.Companion  provideDelegate >com.example.myapplication.core.security.TokenManager.Companion  synchronized >com.example.myapplication.core.security.TokenManager.Companion  	AppLogger ;com.example.myapplication.core.security.TokenRefreshService  Boolean ;com.example.myapplication.core.security.TokenRefreshService  CHECK_INTERVAL_MINUTES ;com.example.myapplication.core.security.TokenRefreshService  CoroutineScope ;com.example.myapplication.core.security.TokenRefreshService  Dispatchers ;com.example.myapplication.core.security.TokenRefreshService  	Exception ;com.example.myapplication.core.security.TokenRefreshService  Job ;com.example.myapplication.core.security.TokenRefreshService  Long ;com.example.myapplication.core.security.TokenRefreshService  REFRESH_THRESHOLD_MINUTES ;com.example.myapplication.core.security.TokenRefreshService  RefreshTokenUseCase ;com.example.myapplication.core.security.TokenRefreshService  
SupervisorJob ;com.example.myapplication.core.security.TokenRefreshService  System ;com.example.myapplication.core.security.TokenRefreshService  TokenManager ;com.example.myapplication.core.security.TokenRefreshService  TokenStatus ;com.example.myapplication.core.security.TokenRefreshService  checkAndRefreshIfNeeded ;com.example.myapplication.core.security.TokenRefreshService  delay ;com.example.myapplication.core.security.TokenRefreshService  forceRefresh ;com.example.myapplication.core.security.TokenRefreshService  getInstance ;com.example.myapplication.core.security.TokenRefreshService  getTimeToTokenExpiration ;com.example.myapplication.core.security.TokenRefreshService  getTokenStatus ;com.example.myapplication.core.security.TokenRefreshService  isActive ;com.example.myapplication.core.security.TokenRefreshService  launch ;com.example.myapplication.core.security.TokenRefreshService  logger ;com.example.myapplication.core.security.TokenRefreshService  maxOf ;com.example.myapplication.core.security.TokenRefreshService  minutes ;com.example.myapplication.core.security.TokenRefreshService  
refreshJob ;com.example.myapplication.core.security.TokenRefreshService  refreshTokenUseCase ;com.example.myapplication.core.security.TokenRefreshService  serviceScope ;com.example.myapplication.core.security.TokenRefreshService  startAutoRefresh ;com.example.myapplication.core.security.TokenRefreshService  stopAutoRefresh ;com.example.myapplication.core.security.TokenRefreshService  tokenManager ;com.example.myapplication.core.security.TokenRefreshService  	AppLogger Ecom.example.myapplication.core.security.TokenRefreshService.Companion  CHECK_INTERVAL_MINUTES Ecom.example.myapplication.core.security.TokenRefreshService.Companion  CoroutineScope Ecom.example.myapplication.core.security.TokenRefreshService.Companion  Dispatchers Ecom.example.myapplication.core.security.TokenRefreshService.Companion  REFRESH_THRESHOLD_MINUTES Ecom.example.myapplication.core.security.TokenRefreshService.Companion  
SupervisorJob Ecom.example.myapplication.core.security.TokenRefreshService.Companion  System Ecom.example.myapplication.core.security.TokenRefreshService.Companion  TokenStatus Ecom.example.myapplication.core.security.TokenRefreshService.Companion  checkAndRefreshIfNeeded Ecom.example.myapplication.core.security.TokenRefreshService.Companion  delay Ecom.example.myapplication.core.security.TokenRefreshService.Companion  getInstance Ecom.example.myapplication.core.security.TokenRefreshService.Companion  isActive Ecom.example.myapplication.core.security.TokenRefreshService.Companion  launch Ecom.example.myapplication.core.security.TokenRefreshService.Companion  logger Ecom.example.myapplication.core.security.TokenRefreshService.Companion  maxOf Ecom.example.myapplication.core.security.TokenRefreshService.Companion  minutes Ecom.example.myapplication.core.security.TokenRefreshService.Companion  TokenRefreshService Bcom.example.myapplication.core.security.TokenRefreshServiceFactory  create Bcom.example.myapplication.core.security.TokenRefreshServiceFactory  EXPIRED 3com.example.myapplication.core.security.TokenStatus  INVALID 3com.example.myapplication.core.security.TokenStatus  
NEEDS_REFRESH 3com.example.myapplication.core.security.TokenStatus  NOT_AUTHENTICATED 3com.example.myapplication.core.security.TokenStatus  VALID 3com.example.myapplication.core.security.TokenStatus  name 3com.example.myapplication.core.security.TokenStatus  
ApiService )com.example.myapplication.data.remote.api  BaseResponseDto )com.example.myapplication.data.remote.api  Body )com.example.myapplication.data.remote.api  GsonConverterFactory )com.example.myapplication.data.remote.api  HttpLoggingInterceptor )com.example.myapplication.data.remote.api  LoginRequestDto )com.example.myapplication.data.remote.api  LoginResponseDto )com.example.myapplication.data.remote.api  
NetworkConfig )com.example.myapplication.data.remote.api  OkHttpClient )com.example.myapplication.data.remote.api  POST )com.example.myapplication.data.remote.api  RegisterRequestDto )com.example.myapplication.data.remote.api  Response )com.example.myapplication.data.remote.api  Retrofit )com.example.myapplication.data.remote.api  RetrofitClient )com.example.myapplication.data.remote.api  TimeUnit )com.example.myapplication.data.remote.api  apply )com.example.myapplication.data.remote.api  
getBaseUrl )com.example.myapplication.data.remote.api  getValue )com.example.myapplication.data.remote.api  java )com.example.myapplication.data.remote.api  lazy )com.example.myapplication.data.remote.api  provideDelegate )com.example.myapplication.data.remote.api  login 4com.example.myapplication.data.remote.api.ApiService  register 4com.example.myapplication.data.remote.api.ApiService  BASE_URL 7com.example.myapplication.data.remote.api.NetworkConfig  
getBaseUrl 7com.example.myapplication.data.remote.api.NetworkConfig  
ApiService 8com.example.myapplication.data.remote.api.RetrofitClient  BASE_URL 8com.example.myapplication.data.remote.api.RetrofitClient  GsonConverterFactory 8com.example.myapplication.data.remote.api.RetrofitClient  HttpLoggingInterceptor 8com.example.myapplication.data.remote.api.RetrofitClient  
NetworkConfig 8com.example.myapplication.data.remote.api.RetrofitClient  OkHttpClient 8com.example.myapplication.data.remote.api.RetrofitClient  Retrofit 8com.example.myapplication.data.remote.api.RetrofitClient  TimeUnit 8com.example.myapplication.data.remote.api.RetrofitClient  apply 8com.example.myapplication.data.remote.api.RetrofitClient  
getBaseUrl 8com.example.myapplication.data.remote.api.RetrofitClient  getValue 8com.example.myapplication.data.remote.api.RetrofitClient  java 8com.example.myapplication.data.remote.api.RetrofitClient  lazy 8com.example.myapplication.data.remote.api.RetrofitClient  okHttpClient 8com.example.myapplication.data.remote.api.RetrofitClient  provideDelegate 8com.example.myapplication.data.remote.api.RetrofitClient  Any )com.example.myapplication.data.remote.dto  BaseRequestDto )com.example.myapplication.data.remote.dto  BaseResponseDto )com.example.myapplication.data.remote.dto  Boolean )com.example.myapplication.data.remote.dto  ErrorResponseDto )com.example.myapplication.data.remote.dto  Int )com.example.myapplication.data.remote.dto  LoginRequestDto )com.example.myapplication.data.remote.dto  LoginResponseDto )com.example.myapplication.data.remote.dto  Long )com.example.myapplication.data.remote.dto  MetaDto )com.example.myapplication.data.remote.dto  RegisterRequestDto )com.example.myapplication.data.remote.dto  SerializedName )com.example.myapplication.data.remote.dto  String )com.example.myapplication.data.remote.dto  System )com.example.myapplication.data.remote.dto  T )com.example.myapplication.data.remote.dto  UserDto )com.example.myapplication.data.remote.dto  UserLoginDto )com.example.myapplication.data.remote.dto  data 9com.example.myapplication.data.remote.dto.BaseResponseDto  message 9com.example.myapplication.data.remote.dto.BaseResponseDto  success 9com.example.myapplication.data.remote.dto.BaseResponseDto  code :com.example.myapplication.data.remote.dto.ErrorResponseDto  error :com.example.myapplication.data.remote.dto.ErrorResponseDto  message :com.example.myapplication.data.remote.dto.ErrorResponseDto  
statusCode :com.example.myapplication.data.remote.dto.ErrorResponseDto  accessToken :com.example.myapplication.data.remote.dto.LoginResponseDto  	expiresIn :com.example.myapplication.data.remote.dto.LoginResponseDto  refreshToken :com.example.myapplication.data.remote.dto.LoginResponseDto  	tokenType :com.example.myapplication.data.remote.dto.LoginResponseDto  user :com.example.myapplication.data.remote.dto.LoginResponseDto  avatar 6com.example.myapplication.data.remote.dto.UserLoginDto  	createdAt 6com.example.myapplication.data.remote.dto.UserLoginDto  email 6com.example.myapplication.data.remote.dto.UserLoginDto  fullName 6com.example.myapplication.data.remote.dto.UserLoginDto  id 6com.example.myapplication.data.remote.dto.UserLoginDto  isActive 6com.example.myapplication.data.remote.dto.UserLoginDto  	updatedAt 6com.example.myapplication.data.remote.dto.UserLoginDto  username 6com.example.myapplication.data.remote.dto.UserLoginDto  Any )com.example.myapplication.data.repository  
ApiService )com.example.myapplication.data.repository  	AuthError )com.example.myapplication.data.repository  AuthRepository )com.example.myapplication.data.repository  AuthRepositoryImpl )com.example.myapplication.data.repository  ErrorResponseDto )com.example.myapplication.data.repository  	Exception )com.example.myapplication.data.repository  Gson )com.example.myapplication.data.repository  
HttpException )com.example.myapplication.data.repository  IOException )com.example.myapplication.data.repository  Int )com.example.myapplication.data.repository  List )com.example.myapplication.data.repository  LoginRequest )com.example.myapplication.data.repository  LoginRequestDto )com.example.myapplication.data.repository  
LoginResponse )com.example.myapplication.data.repository  RegisterRequest )com.example.myapplication.data.repository  RegisterRequestDto )com.example.myapplication.data.repository  RegisterResponse )com.example.myapplication.data.repository  Resource )com.example.myapplication.data.repository  String )com.example.myapplication.data.repository  	UserLogin )com.example.myapplication.data.repository  com )com.example.myapplication.data.repository  java )com.example.myapplication.data.repository  joinToString )com.example.myapplication.data.repository  	AuthError <com.example.myapplication.data.repository.AuthRepositoryImpl  ErrorResponseDto <com.example.myapplication.data.repository.AuthRepositoryImpl  Gson <com.example.myapplication.data.repository.AuthRepositoryImpl  LoginRequestDto <com.example.myapplication.data.repository.AuthRepositoryImpl  
LoginResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  RegisterRequestDto <com.example.myapplication.data.repository.AuthRepositoryImpl  RegisterResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  Resource <com.example.myapplication.data.repository.AuthRepositoryImpl  	UserLogin <com.example.myapplication.data.repository.AuthRepositoryImpl  
apiService <com.example.myapplication.data.repository.AuthRepositoryImpl  handleErrorResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  handleRegisterErrorResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  handleRegisterSuccessResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  handleSuccessResponse <com.example.myapplication.data.repository.AuthRepositoryImpl  java <com.example.myapplication.data.repository.AuthRepositoryImpl  joinToString <com.example.myapplication.data.repository.AuthRepositoryImpl  example -com.example.myapplication.data.repository.com  
myapplication 5com.example.myapplication.data.repository.com.example  data Ccom.example.myapplication.data.repository.com.example.myapplication  remote Hcom.example.myapplication.data.repository.com.example.myapplication.data  dto Ocom.example.myapplication.data.repository.com.example.myapplication.data.remote  BaseResponseDto Scom.example.myapplication.data.repository.com.example.myapplication.data.remote.dto  LoginResponseDto Scom.example.myapplication.data.repository.com.example.myapplication.data.remote.dto  	AuthError &com.example.myapplication.domain.model  	AuthToken &com.example.myapplication.domain.model  Boolean &com.example.myapplication.domain.model  Int &com.example.myapplication.domain.model  LoginRequest &com.example.myapplication.domain.model  
LoginResponse &com.example.myapplication.domain.model  Long &com.example.myapplication.domain.model  RegisterRequest &com.example.myapplication.domain.model  RegisterResponse &com.example.myapplication.domain.model  String &com.example.myapplication.domain.model  	UserLogin &com.example.myapplication.domain.model  message 0com.example.myapplication.domain.model.AuthError  accessToken 0com.example.myapplication.domain.model.AuthToken  	expiresIn 0com.example.myapplication.domain.model.AuthToken  refreshToken 0com.example.myapplication.domain.model.AuthToken  	tokenType 0com.example.myapplication.domain.model.AuthToken  password 3com.example.myapplication.domain.model.LoginRequest  usernameOrEmail 3com.example.myapplication.domain.model.LoginRequest  accessToken 4com.example.myapplication.domain.model.LoginResponse  	expiresIn 4com.example.myapplication.domain.model.LoginResponse  refreshToken 4com.example.myapplication.domain.model.LoginResponse  	tokenType 4com.example.myapplication.domain.model.LoginResponse  user 4com.example.myapplication.domain.model.LoginResponse  email 6com.example.myapplication.domain.model.RegisterRequest  fullName 6com.example.myapplication.domain.model.RegisterRequest  password 6com.example.myapplication.domain.model.RegisterRequest  username 6com.example.myapplication.domain.model.RegisterRequest  accessToken 7com.example.myapplication.domain.model.RegisterResponse  	expiresIn 7com.example.myapplication.domain.model.RegisterResponse  refreshToken 7com.example.myapplication.domain.model.RegisterResponse  	tokenType 7com.example.myapplication.domain.model.RegisterResponse  user 7com.example.myapplication.domain.model.RegisterResponse  avatar 0com.example.myapplication.domain.model.UserLogin  	createdAt 0com.example.myapplication.domain.model.UserLogin  email 0com.example.myapplication.domain.model.UserLogin  fullName 0com.example.myapplication.domain.model.UserLogin  id 0com.example.myapplication.domain.model.UserLogin  isActive 0com.example.myapplication.domain.model.UserLogin  	updatedAt 0com.example.myapplication.domain.model.UserLogin  username 0com.example.myapplication.domain.model.UserLogin  AuthRepository +com.example.myapplication.domain.repository  LoginRequest +com.example.myapplication.domain.repository  
LoginResponse +com.example.myapplication.domain.repository  RegisterRequest +com.example.myapplication.domain.repository  RegisterResponse +com.example.myapplication.domain.repository  Resource +com.example.myapplication.domain.repository  String +com.example.myapplication.domain.repository  login :com.example.myapplication.domain.repository.AuthRepository  refreshToken :com.example.myapplication.domain.repository.AuthRepository  register :com.example.myapplication.domain.repository.AuthRepository  AppConstants (com.example.myapplication.domain.useCase  AuthRepository (com.example.myapplication.domain.useCase  Boolean (com.example.myapplication.domain.useCase  LoginRequest (com.example.myapplication.domain.useCase  
LoginResponse (com.example.myapplication.domain.useCase  LoginUseCase (com.example.myapplication.domain.useCase  Regex (com.example.myapplication.domain.useCase  RegisterRequest (com.example.myapplication.domain.useCase  RegisterResponse (com.example.myapplication.domain.useCase  RegisterUseCase (com.example.myapplication.domain.useCase  Resource (com.example.myapplication.domain.useCase  String (com.example.myapplication.domain.useCase  android (com.example.myapplication.domain.useCase  isBlank (com.example.myapplication.domain.useCase  
isNotBlank (com.example.myapplication.domain.useCase  	lowercase (com.example.myapplication.domain.useCase  matches (com.example.myapplication.domain.useCase  takeIf (com.example.myapplication.domain.useCase  trim (com.example.myapplication.domain.useCase  AppConstants 5com.example.myapplication.domain.useCase.LoginUseCase  LoginRequest 5com.example.myapplication.domain.useCase.LoginUseCase  Resource 5com.example.myapplication.domain.useCase.LoginUseCase  authRepository 5com.example.myapplication.domain.useCase.LoginUseCase  invoke 5com.example.myapplication.domain.useCase.LoginUseCase  isBlank 5com.example.myapplication.domain.useCase.LoginUseCase  trim 5com.example.myapplication.domain.useCase.LoginUseCase  AppConstants 8com.example.myapplication.domain.useCase.RegisterUseCase  Regex 8com.example.myapplication.domain.useCase.RegisterUseCase  RegisterRequest 8com.example.myapplication.domain.useCase.RegisterUseCase  Resource 8com.example.myapplication.domain.useCase.RegisterUseCase  android 8com.example.myapplication.domain.useCase.RegisterUseCase  authRepository 8com.example.myapplication.domain.useCase.RegisterUseCase  invoke 8com.example.myapplication.domain.useCase.RegisterUseCase  isBlank 8com.example.myapplication.domain.useCase.RegisterUseCase  
isNotBlank 8com.example.myapplication.domain.useCase.RegisterUseCase  isValidEmail 8com.example.myapplication.domain.useCase.RegisterUseCase  	lowercase 8com.example.myapplication.domain.useCase.RegisterUseCase  matches 8com.example.myapplication.domain.useCase.RegisterUseCase  takeIf 8com.example.myapplication.domain.useCase.RegisterUseCase  trim 8com.example.myapplication.domain.useCase.RegisterUseCase  AuthRepository 6com.example.myapplication.features.auth.domain.usecase  	AuthToken 6com.example.myapplication.features.auth.domain.usecase  Boolean 6com.example.myapplication.features.auth.domain.usecase  	Exception 6com.example.myapplication.features.auth.domain.usecase  
LogoutUseCase 6com.example.myapplication.features.auth.domain.usecase  RefreshTokenUseCase 6com.example.myapplication.features.auth.domain.usecase  Resource 6com.example.myapplication.features.auth.domain.usecase  TokenManager 6com.example.myapplication.features.auth.domain.usecase  Unit 6com.example.myapplication.features.auth.domain.usecase  
isNullOrEmpty 6com.example.myapplication.features.auth.domain.usecase  Resource Dcom.example.myapplication.features.auth.domain.usecase.LogoutUseCase  Unit Dcom.example.myapplication.features.auth.domain.usecase.LogoutUseCase  invoke Dcom.example.myapplication.features.auth.domain.usecase.LogoutUseCase  tokenManager Dcom.example.myapplication.features.auth.domain.usecase.LogoutUseCase  	AuthToken Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  Resource Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  authRepository Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  forceRefresh Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  invoke Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  
isNullOrEmpty Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  tokenManager Jcom.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase  Error ?com.example.myapplication.features.auth.domain.usecase.Resource  Loading ?com.example.myapplication.features.auth.domain.usecase.Resource  Success ?com.example.myapplication.features.auth.domain.usecase.Resource  	Alignment :com.example.myapplication.features.auth.presentation.login  AppContainer :com.example.myapplication.features.auth.presentation.login  Arrangement :com.example.myapplication.features.auth.presentation.login  AuthenticationManager :com.example.myapplication.features.auth.presentation.login  Boolean :com.example.myapplication.features.auth.presentation.login  Button :com.example.myapplication.features.auth.presentation.login  Card :com.example.myapplication.features.auth.presentation.login  CardDefaults :com.example.myapplication.features.auth.presentation.login  CircularProgressIndicator :com.example.myapplication.features.auth.presentation.login  Class :com.example.myapplication.features.auth.presentation.login  Column :com.example.myapplication.features.auth.presentation.login  
Composable :com.example.myapplication.features.auth.presentation.login  ExperimentalMaterial3Api :com.example.myapplication.features.auth.presentation.login  FocusDirection :com.example.myapplication.features.auth.presentation.login  
FontWeight :com.example.myapplication.features.auth.presentation.login  Icon :com.example.myapplication.features.auth.presentation.login  
IconButton :com.example.myapplication.features.auth.presentation.login  Icons :com.example.myapplication.features.auth.presentation.login  IllegalArgumentException :com.example.myapplication.features.auth.presentation.login  	ImeAction :com.example.myapplication.features.auth.presentation.login  KeyboardActions :com.example.myapplication.features.auth.presentation.login  KeyboardOptions :com.example.myapplication.features.auth.presentation.login  KeyboardType :com.example.myapplication.features.auth.presentation.login  LaunchedEffect :com.example.myapplication.features.auth.presentation.login  
LoginEvent :com.example.myapplication.features.auth.presentation.login  
LoginResponse :com.example.myapplication.features.auth.presentation.login  LoginResult :com.example.myapplication.features.auth.presentation.login  LoginScreen :com.example.myapplication.features.auth.presentation.login  LoginScreenPreview :com.example.myapplication.features.auth.presentation.login  LoginUiState :com.example.myapplication.features.auth.presentation.login  LoginUseCase :com.example.myapplication.features.auth.presentation.login  LoginViewModel :com.example.myapplication.features.auth.presentation.login  LoginViewModelFactory :com.example.myapplication.features.auth.presentation.login  
MaterialTheme :com.example.myapplication.features.auth.presentation.login  Modifier :com.example.myapplication.features.auth.presentation.login  MutableStateFlow :com.example.myapplication.features.auth.presentation.login  OptIn :com.example.myapplication.features.auth.presentation.login  OutlinedButton :com.example.myapplication.features.auth.presentation.login  OutlinedTextField :com.example.myapplication.features.auth.presentation.login  PasswordVisualTransformation :com.example.myapplication.features.auth.presentation.login  Preview :com.example.myapplication.features.auth.presentation.login  Resource :com.example.myapplication.features.auth.presentation.login  Spacer :com.example.myapplication.features.auth.presentation.login  	StateFlow :com.example.myapplication.features.auth.presentation.login  String :com.example.myapplication.features.auth.presentation.login  Suppress :com.example.myapplication.features.auth.presentation.login  T :com.example.myapplication.features.auth.presentation.login  Text :com.example.myapplication.features.auth.presentation.login  
TextButton :com.example.myapplication.features.auth.presentation.login  UiEvent :com.example.myapplication.features.auth.presentation.login  Unit :com.example.myapplication.features.auth.presentation.login  	ViewModel :com.example.myapplication.features.auth.presentation.login  ViewModelProvider :com.example.myapplication.features.auth.presentation.login  VisualTransformation :com.example.myapplication.features.auth.presentation.login  _uiState :com.example.myapplication.features.auth.presentation.login  asStateFlow :com.example.myapplication.features.auth.presentation.login  authenticationManager :com.example.myapplication.features.auth.presentation.login  
cardColors :com.example.myapplication.features.auth.presentation.login  fillMaxSize :com.example.myapplication.features.auth.presentation.login  fillMaxWidth :com.example.myapplication.features.auth.presentation.login  getValue :com.example.myapplication.features.auth.presentation.login  height :com.example.myapplication.features.auth.presentation.login  
isNotBlank :com.example.myapplication.features.auth.presentation.login  java :com.example.myapplication.features.auth.presentation.login  launch :com.example.myapplication.features.auth.presentation.login  let :com.example.myapplication.features.auth.presentation.login  loginUseCase :com.example.myapplication.features.auth.presentation.login  padding :com.example.myapplication.features.auth.presentation.login  provideDelegate :com.example.myapplication.features.auth.presentation.login  size :com.example.myapplication.features.auth.presentation.login  
ClearError Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  Login Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  
LoginEvent Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  PasswordChanged Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  String Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  TogglePasswordVisibility Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  UsernameOrEmailChanged Ecom.example.myapplication.features.auth.presentation.login.LoginEvent  value Ucom.example.myapplication.features.auth.presentation.login.LoginEvent.PasswordChanged  value \com.example.myapplication.features.auth.presentation.login.LoginEvent.UsernameOrEmailChanged  Error Fcom.example.myapplication.features.auth.presentation.login.LoginResult  
LoginResponse Fcom.example.myapplication.features.auth.presentation.login.LoginResult  LoginResult Fcom.example.myapplication.features.auth.presentation.login.LoginResult  String Fcom.example.myapplication.features.auth.presentation.login.LoginResult  Success Fcom.example.myapplication.features.auth.presentation.login.LoginResult  
loginResponse Ncom.example.myapplication.features.auth.presentation.login.LoginResult.Success  copy Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  errorMessage Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  	isLoading Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  isPasswordVisible Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  loginResult Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  password Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  usernameOrEmail Gcom.example.myapplication.features.auth.presentation.login.LoginUiState  LoginResult Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  LoginUiState Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  MutableStateFlow Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  _uiState Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  asStateFlow Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  authenticationManager Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  launch Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  loginUseCase Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  onEvent Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  performLogin Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  uiState Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  viewModelScope Icom.example.myapplication.features.auth.presentation.login.LoginViewModel  IllegalArgumentException Pcom.example.myapplication.features.auth.presentation.login.LoginViewModelFactory  LoginViewModel Pcom.example.myapplication.features.auth.presentation.login.LoginViewModelFactory  appContainer Pcom.example.myapplication.features.auth.presentation.login.LoginViewModelFactory  java Pcom.example.myapplication.features.auth.presentation.login.LoginViewModelFactory  Error Ccom.example.myapplication.features.auth.presentation.login.Resource  Loading Ccom.example.myapplication.features.auth.presentation.login.Resource  Success Ccom.example.myapplication.features.auth.presentation.login.Resource  Factory Lcom.example.myapplication.features.auth.presentation.login.ViewModelProvider  	Alignment <com.example.myapplication.features.auth.presentation.network  AppConstants <com.example.myapplication.features.auth.presentation.network  Button <com.example.myapplication.features.auth.presentation.network  Card <com.example.myapplication.features.auth.presentation.network  CardDefaults <com.example.myapplication.features.auth.presentation.network  CircularProgressIndicator <com.example.myapplication.features.auth.presentation.network  Column <com.example.myapplication.features.auth.presentation.network  
Composable <com.example.myapplication.features.auth.presentation.network  	Exception <com.example.myapplication.features.auth.presentation.network  ExperimentalMaterial3Api <com.example.myapplication.features.auth.presentation.network  
FontWeight <com.example.myapplication.features.auth.presentation.network  HttpURLConnection <com.example.myapplication.features.auth.presentation.network  List <com.example.myapplication.features.auth.presentation.network  
MaterialTheme <com.example.myapplication.features.auth.presentation.network  Modifier <com.example.myapplication.features.auth.presentation.network  NetworkTestScreen <com.example.myapplication.features.auth.presentation.network  OptIn <com.example.myapplication.features.auth.presentation.network  String <com.example.myapplication.features.auth.presentation.network  Text <com.example.myapplication.features.auth.presentation.network  
cardColors <com.example.myapplication.features.auth.presentation.network  	emptyList <com.example.myapplication.features.auth.presentation.network  fillMaxSize <com.example.myapplication.features.auth.presentation.network  fillMaxWidth <com.example.myapplication.features.auth.presentation.network  forEach <com.example.myapplication.features.auth.presentation.network  getValue <com.example.myapplication.features.auth.presentation.network  
isNotEmpty <com.example.myapplication.features.auth.presentation.network  launch <com.example.myapplication.features.auth.presentation.network  listOf <com.example.myapplication.features.auth.presentation.network  
mutableListOf <com.example.myapplication.features.auth.presentation.network  mutableStateOf <com.example.myapplication.features.auth.presentation.network  padding <com.example.myapplication.features.auth.presentation.network  provideDelegate <com.example.myapplication.features.auth.presentation.network  remember <com.example.myapplication.features.auth.presentation.network  rememberCoroutineScope <com.example.myapplication.features.auth.presentation.network  setValue <com.example.myapplication.features.auth.presentation.network  size <com.example.myapplication.features.auth.presentation.network  testConnectivity <com.example.myapplication.features.auth.presentation.network  	Alignment =com.example.myapplication.features.auth.presentation.register  AppContainer =com.example.myapplication.features.auth.presentation.register  Arrangement =com.example.myapplication.features.auth.presentation.register  AuthenticationManager =com.example.myapplication.features.auth.presentation.register  Boolean =com.example.myapplication.features.auth.presentation.register  Button =com.example.myapplication.features.auth.presentation.register  Card =com.example.myapplication.features.auth.presentation.register  CardDefaults =com.example.myapplication.features.auth.presentation.register  CircularProgressIndicator =com.example.myapplication.features.auth.presentation.register  Class =com.example.myapplication.features.auth.presentation.register  Column =com.example.myapplication.features.auth.presentation.register  
Composable =com.example.myapplication.features.auth.presentation.register  ExperimentalMaterial3Api =com.example.myapplication.features.auth.presentation.register  FocusDirection =com.example.myapplication.features.auth.presentation.register  
FontWeight =com.example.myapplication.features.auth.presentation.register  Icon =com.example.myapplication.features.auth.presentation.register  
IconButton =com.example.myapplication.features.auth.presentation.register  Icons =com.example.myapplication.features.auth.presentation.register  IllegalArgumentException =com.example.myapplication.features.auth.presentation.register  	ImeAction =com.example.myapplication.features.auth.presentation.register  KeyboardActions =com.example.myapplication.features.auth.presentation.register  KeyboardOptions =com.example.myapplication.features.auth.presentation.register  KeyboardType =com.example.myapplication.features.auth.presentation.register  LaunchedEffect =com.example.myapplication.features.auth.presentation.register  
MaterialTheme =com.example.myapplication.features.auth.presentation.register  Modifier =com.example.myapplication.features.auth.presentation.register  MutableStateFlow =com.example.myapplication.features.auth.presentation.register  OptIn =com.example.myapplication.features.auth.presentation.register  OutlinedTextField =com.example.myapplication.features.auth.presentation.register  PasswordVisualTransformation =com.example.myapplication.features.auth.presentation.register  Preview =com.example.myapplication.features.auth.presentation.register  
RegisterEvent =com.example.myapplication.features.auth.presentation.register  RegisterResponse =com.example.myapplication.features.auth.presentation.register  RegisterResult =com.example.myapplication.features.auth.presentation.register  RegisterScreen =com.example.myapplication.features.auth.presentation.register  RegisterScreenPreview =com.example.myapplication.features.auth.presentation.register  RegisterUiState =com.example.myapplication.features.auth.presentation.register  RegisterUseCase =com.example.myapplication.features.auth.presentation.register  RegisterViewModel =com.example.myapplication.features.auth.presentation.register  RegisterViewModelFactory =com.example.myapplication.features.auth.presentation.register  Resource =com.example.myapplication.features.auth.presentation.register  Spacer =com.example.myapplication.features.auth.presentation.register  	StateFlow =com.example.myapplication.features.auth.presentation.register  String =com.example.myapplication.features.auth.presentation.register  Suppress =com.example.myapplication.features.auth.presentation.register  T =com.example.myapplication.features.auth.presentation.register  Text =com.example.myapplication.features.auth.presentation.register  	TextAlign =com.example.myapplication.features.auth.presentation.register  
TextButton =com.example.myapplication.features.auth.presentation.register  UiEvent =com.example.myapplication.features.auth.presentation.register  Unit =com.example.myapplication.features.auth.presentation.register  	ViewModel =com.example.myapplication.features.auth.presentation.register  ViewModelProvider =com.example.myapplication.features.auth.presentation.register  VisualTransformation =com.example.myapplication.features.auth.presentation.register  _uiState =com.example.myapplication.features.auth.presentation.register  asStateFlow =com.example.myapplication.features.auth.presentation.register  authenticationManager =com.example.myapplication.features.auth.presentation.register  
cardColors =com.example.myapplication.features.auth.presentation.register  fillMaxSize =com.example.myapplication.features.auth.presentation.register  fillMaxWidth =com.example.myapplication.features.auth.presentation.register  getValue =com.example.myapplication.features.auth.presentation.register  height =com.example.myapplication.features.auth.presentation.register  
isNotBlank =com.example.myapplication.features.auth.presentation.register  java =com.example.myapplication.features.auth.presentation.register  launch =com.example.myapplication.features.auth.presentation.register  let =com.example.myapplication.features.auth.presentation.register  padding =com.example.myapplication.features.auth.presentation.register  provideDelegate =com.example.myapplication.features.auth.presentation.register  registerUseCase =com.example.myapplication.features.auth.presentation.register  size =com.example.myapplication.features.auth.presentation.register  takeIf =com.example.myapplication.features.auth.presentation.register  
ClearError Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  EmailChanged Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  FullNameChanged Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  NavigateToLogin Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  PasswordChanged Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  Register Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  
RegisterEvent Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  String Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  TogglePasswordVisibility Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  UsernameChanged Kcom.example.myapplication.features.auth.presentation.register.RegisterEvent  value Xcom.example.myapplication.features.auth.presentation.register.RegisterEvent.EmailChanged  value [com.example.myapplication.features.auth.presentation.register.RegisterEvent.FullNameChanged  value [com.example.myapplication.features.auth.presentation.register.RegisterEvent.PasswordChanged  value [com.example.myapplication.features.auth.presentation.register.RegisterEvent.UsernameChanged  Error Lcom.example.myapplication.features.auth.presentation.register.RegisterResult  RegisterResponse Lcom.example.myapplication.features.auth.presentation.register.RegisterResult  RegisterResult Lcom.example.myapplication.features.auth.presentation.register.RegisterResult  String Lcom.example.myapplication.features.auth.presentation.register.RegisterResult  Success Lcom.example.myapplication.features.auth.presentation.register.RegisterResult  registerResponse Tcom.example.myapplication.features.auth.presentation.register.RegisterResult.Success  copy Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  email Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  errorMessage Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  fullName Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  	isLoading Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  isPasswordVisible Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  password Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  registerResult Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  username Mcom.example.myapplication.features.auth.presentation.register.RegisterUiState  MutableStateFlow Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  RegisterResult Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  RegisterUiState Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  _uiState Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  asStateFlow Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  authenticationManager Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  
isNotBlank Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  launch Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  onEvent Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  performRegister Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  registerUseCase Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  takeIf Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  uiState Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  viewModelScope Ocom.example.myapplication.features.auth.presentation.register.RegisterViewModel  IllegalArgumentException Vcom.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory  RegisterViewModel Vcom.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory  appContainer Vcom.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory  java Vcom.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory  Error Fcom.example.myapplication.features.auth.presentation.register.Resource  Loading Fcom.example.myapplication.features.auth.presentation.register.Resource  Success Fcom.example.myapplication.features.auth.presentation.register.Resource  Factory Ocom.example.myapplication.features.auth.presentation.register.ViewModelProvider  AppContainer 5com.example.myapplication.features.debug.presentation  Arrangement 5com.example.myapplication.features.debug.presentation  Button 5com.example.myapplication.features.debug.presentation  Card 5com.example.myapplication.features.debug.presentation  CardDefaults 5com.example.myapplication.features.debug.presentation  Column 5com.example.myapplication.features.debug.presentation  
Composable 5com.example.myapplication.features.debug.presentation  DebugScreen 5com.example.myapplication.features.debug.presentation  DebugScreenPreview 5com.example.myapplication.features.debug.presentation  	Exception 5com.example.myapplication.features.debug.presentation  
FontWeight 5com.example.myapplication.features.debug.presentation  
MaterialTheme 5com.example.myapplication.features.debug.presentation  Modifier 5com.example.myapplication.features.debug.presentation  OutlinedButton 5com.example.myapplication.features.debug.presentation  Preview 5com.example.myapplication.features.debug.presentation  Spacer 5com.example.myapplication.features.debug.presentation  String 5com.example.myapplication.features.debug.presentation  System 5com.example.myapplication.features.debug.presentation  Text 5com.example.myapplication.features.debug.presentation  TokenStatus 5com.example.myapplication.features.debug.presentation  Unit 5com.example.myapplication.features.debug.presentation  
cardColors 5com.example.myapplication.features.debug.presentation  fillMaxWidth 5com.example.myapplication.features.debug.presentation  height 5com.example.myapplication.features.debug.presentation  launch 5com.example.myapplication.features.debug.presentation  let 5com.example.myapplication.features.debug.presentation  padding 5com.example.myapplication.features.debug.presentation  provideDelegate 5com.example.myapplication.features.debug.presentation  spacedBy 5com.example.myapplication.features.debug.presentation  	Alignment 4com.example.myapplication.features.main.presentation  Arrangement 4com.example.myapplication.features.main.presentation  Button 4com.example.myapplication.features.main.presentation  Column 4com.example.myapplication.features.main.presentation  
Composable 4com.example.myapplication.features.main.presentation  
FontWeight 4com.example.myapplication.features.main.presentation  
MainScreen 4com.example.myapplication.features.main.presentation  MainScreenPreview 4com.example.myapplication.features.main.presentation  Modifier 4com.example.myapplication.features.main.presentation  OutlinedButton 4com.example.myapplication.features.main.presentation  Preview 4com.example.myapplication.features.main.presentation  Spacer 4com.example.myapplication.features.main.presentation  String 4com.example.myapplication.features.main.presentation  Text 4com.example.myapplication.features.main.presentation  Unit 4com.example.myapplication.features.main.presentation  fillMaxSize 4com.example.myapplication.features.main.presentation  fillMaxWidth 4com.example.myapplication.features.main.presentation  height 4com.example.myapplication.features.main.presentation  padding 4com.example.myapplication.features.main.presentation  	Alignment 6com.example.myapplication.features.splash.presentation  Arrangement 6com.example.myapplication.features.splash.presentation  Boolean 6com.example.myapplication.features.splash.presentation  CircularProgressIndicator 6com.example.myapplication.features.splash.presentation  Column 6com.example.myapplication.features.splash.presentation  
Composable 6com.example.myapplication.features.splash.presentation  
FontWeight 6com.example.myapplication.features.splash.presentation  Image 6com.example.myapplication.features.splash.presentation  
MaterialTheme 6com.example.myapplication.features.splash.presentation  Modifier 6com.example.myapplication.features.splash.presentation  Preview 6com.example.myapplication.features.splash.presentation  
RepeatMode 6com.example.myapplication.features.splash.presentation  Spacer 6com.example.myapplication.features.splash.presentation  SplashScreen 6com.example.myapplication.features.splash.presentation  SplashScreenPreview 6com.example.myapplication.features.splash.presentation  SplashScreenWithError 6com.example.myapplication.features.splash.presentation  SplashScreenWithErrorPreview 6com.example.myapplication.features.splash.presentation  String 6com.example.myapplication.features.splash.presentation  Text 6com.example.myapplication.features.splash.presentation  Unit 6com.example.myapplication.features.splash.presentation  alpha 6com.example.myapplication.features.splash.presentation  android 6com.example.myapplication.features.splash.presentation  androidx 6com.example.myapplication.features.splash.presentation  buttonColors 6com.example.myapplication.features.splash.presentation  height 6com.example.myapplication.features.splash.presentation  painterResource 6com.example.myapplication.features.splash.presentation  provideDelegate 6com.example.myapplication.features.splash.presentation  size 6com.example.myapplication.features.splash.presentation  Boolean "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  
MaterialTheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  Gson com.google.gson  fromJson com.google.gson.Gson  SerializedName com.google.gson.annotations  IOException java.io  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  SecurityException 	java.lang  isAssignableFrom java.lang.Class  
simpleName java.lang.Class  message java.lang.Exception  currentTimeMillis java.lang.System  HttpURLConnection java.net  URL java.net  connectTimeout java.net.HttpURLConnection  
disconnect java.net.HttpURLConnection  readTimeout java.net.HttpURLConnection  
requestMethod java.net.HttpURLConnection  responseCode java.net.HttpURLConnection  openConnection java.net.URL  connectTimeout java.net.URLConnection  readTimeout java.net.URLConnection  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Date 	java.util  Locale 	java.util  
getDefault java.util.Locale  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  CharSequence kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  Lazy kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Suppress kotlin  	Throwable kotlin  also kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  synchronized kotlin  takeIf kotlin  	AppLogger 
kotlin.Any  getInstance 
kotlin.Any  java 
kotlin.Any  not kotlin.Boolean  CRITICAL kotlin.Enum  	Companion kotlin.Enum  DEBUG kotlin.Enum  ERROR kotlin.Enum  INFO kotlin.Enum  Int kotlin.Enum  LogLevel kotlin.Enum  String kotlin.Enum  WARNING kotlin.Enum  	uppercase kotlin.Enum  CRITICAL kotlin.Enum.Companion  DEBUG kotlin.Enum.Companion  ERROR kotlin.Enum.Companion  INFO kotlin.Enum.Companion  WARNING kotlin.Enum.Companion  	uppercase kotlin.Enum.Companion  invoke kotlin.Function1  	compareTo 
kotlin.Int  times 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  minutes kotlin.Long  plus kotlin.Long  times kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  contains 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  matches 
kotlin.String  takeIf 
kotlin.String  trim 
kotlin.String  	uppercase 
kotlin.String  message kotlin.Throwable  List kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  maxOf kotlin.collections  
mutableListOf kotlin.collections  none kotlin.collections  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  none kotlin.collections.List  add kotlin.collections.MutableList  maxOf kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  invoke "kotlin.coroutines.SuspendFunction0  Throws 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  contains kotlin.sequences  forEach kotlin.sequences  joinToString kotlin.sequences  maxOf kotlin.sequences  none kotlin.sequences  Regex kotlin.text  contains kotlin.text  forEach kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  matches kotlin.text  maxOf kotlin.text  none kotlin.text  trim kotlin.text  	uppercase kotlin.text  Duration kotlin.time  minutes kotlin.time.Duration.Companion  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  delay kotlinx.coroutines  isActive kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  plus !kotlinx.coroutines.CompletableJob  CHECK_INTERVAL_MINUTES !kotlinx.coroutines.CoroutineScope  LoginResult !kotlinx.coroutines.CoroutineScope  RegisterResult !kotlinx.coroutines.CoroutineScope  Screen !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  authenticationManager !kotlinx.coroutines.CoroutineScope  checkAndRefreshIfNeeded !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  
isNotBlank !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  logger !kotlinx.coroutines.CoroutineScope  loginUseCase !kotlinx.coroutines.CoroutineScope  minutes !kotlinx.coroutines.CoroutineScope  refreshTokenUseCase !kotlinx.coroutines.CoroutineScope  registerUseCase !kotlinx.coroutines.CoroutineScope  takeIf !kotlinx.coroutines.CoroutineScope  testConnectivity !kotlinx.coroutines.CoroutineScope  tokenManager !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  plus kotlinx.coroutines.Job  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  collectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  HttpUrl okhttp3  Interceptor okhttp3  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  ResponseBody okhttp3  toString okhttp3.HttpUrl  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  
newBuilder okhttp3.Request  url okhttp3.Request  build okhttp3.Request.Builder  header okhttp3.Request.Builder  close okhttp3.Response  code okhttp3.Response  string okhttp3.ResponseBody  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  
HttpException 	retrofit2  Response 	retrofit2  Retrofit 	retrofit2  message retrofit2.HttpException  body retrofit2.Response  code retrofit2.Response  	errorBody retrofit2.Response  isSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  BaseResponseDto retrofit2.http  Body retrofit2.http  LoginRequestDto retrofit2.http  LoginResponseDto retrofit2.http  POST retrofit2.http  RegisterRequestDto retrofit2.http  Response retrofit2.http  FloatingActionButton "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  
NavigationBar "androidx.compose.foundation.layout  NavigationBarItem "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  	UserLogin "androidx.compose.foundation.layout  bottomNavItems "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Book +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  AchievementItem .androidx.compose.foundation.layout.ColumnScope  ActivityItem .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BookItem .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  InfoItem .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
LocationOn .androidx.compose.foundation.layout.ColumnScope  NotificationCard .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  Phone .androidx.compose.foundation.layout.ColumnScope  RecommendationCard .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SettingItemRow .androidx.compose.foundation.layout.ColumnScope  StatCard .androidx.compose.foundation.layout.ColumnScope  StatItem .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  getAchievements .androidx.compose.foundation.layout.ColumnScope  getFavoriteBooks .androidx.compose.foundation.layout.ColumnScope  getRecentActivities .androidx.compose.foundation.layout.ColumnScope  getRecommendations .androidx.compose.foundation.layout.ColumnScope  getStatsData .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  last .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Book +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  SettingType +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  StatItem +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  bottomNavItems +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  forEachIndexed +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AchievementsSection .androidx.compose.foundation.lazy.LazyItemScope  FavoriteBooksSection .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  
LogoutSection .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  NotificationSection .androidx.compose.foundation.lazy.LazyItemScope  PersonalInfoSection .androidx.compose.foundation.lazy.LazyItemScope  
ProfileHeader .androidx.compose.foundation.lazy.LazyItemScope  ReadingStatsSection .androidx.compose.foundation.lazy.LazyItemScope  RecentActivitiesSection .androidx.compose.foundation.lazy.LazyItemScope  RecommendationCard .androidx.compose.foundation.lazy.LazyItemScope  RecommendationsSection .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  StatCard .androidx.compose.foundation.lazy.LazyItemScope  
StatsCards .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  
WelcomeHeader .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  getAboutSettings .androidx.compose.foundation.lazy.LazyItemScope  getAppearanceSettings .androidx.compose.foundation.lazy.LazyItemScope  getNotificationSettings .androidx.compose.foundation.lazy.LazyItemScope  getOlderNotifications .androidx.compose.foundation.lazy.LazyItemScope  getPrivacySettings .androidx.compose.foundation.lazy.LazyItemScope  getTodayNotifications .androidx.compose.foundation.lazy.LazyItemScope  getWeekNotifications .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  AchievementsSection .androidx.compose.foundation.lazy.LazyListScope  FavoriteBooksSection .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  
LogoutSection .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  NotificationSection .androidx.compose.foundation.lazy.LazyListScope  PersonalInfoSection .androidx.compose.foundation.lazy.LazyListScope  
ProfileHeader .androidx.compose.foundation.lazy.LazyListScope  ReadingStatsSection .androidx.compose.foundation.lazy.LazyListScope  RecentActivitiesSection .androidx.compose.foundation.lazy.LazyListScope  RecommendationCard .androidx.compose.foundation.lazy.LazyListScope  RecommendationsSection .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  StatCard .androidx.compose.foundation.lazy.LazyListScope  
StatsCards .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  
WelcomeHeader .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  getAboutSettings .androidx.compose.foundation.lazy.LazyListScope  getAppearanceSettings .androidx.compose.foundation.lazy.LazyListScope  getNotificationSettings .androidx.compose.foundation.lazy.LazyListScope  getOlderNotifications .androidx.compose.foundation.lazy.LazyListScope  getPrivacySettings .androidx.compose.foundation.lazy.LazyListScope  getRecommendations .androidx.compose.foundation.lazy.LazyListScope  getStatsData .androidx.compose.foundation.lazy.LazyListScope  getTodayNotifications .androidx.compose.foundation.lazy.LazyListScope  getWeekNotifications .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Outlined %androidx.compose.material.icons.Icons  Book ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  DarkMode ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  Group ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Language ,androidx.compose.material.icons.Icons.Filled  
LocationOn ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Phone ,androidx.compose.material.icons.Icons.Filled  Security ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Shield ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Storage ,androidx.compose.material.icons.Icons.Filled  
TrendingUp ,androidx.compose.material.icons.Icons.Filled  Home .androidx.compose.material.icons.Icons.Outlined  
Notifications .androidx.compose.material.icons.Icons.Outlined  Person .androidx.compose.material.icons.Icons.Outlined  Settings .androidx.compose.material.icons.Icons.Outlined  Book &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  Group &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Language &androidx.compose.material.icons.filled  
LocationOn &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Phone &androidx.compose.material.icons.filled  Security &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Shield &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  Storage &androidx.compose.material.icons.filled  
TrendingUp &androidx.compose.material.icons.filled  Home (androidx.compose.material.icons.outlined  
Notifications (androidx.compose.material.icons.outlined  Person (androidx.compose.material.icons.outlined  Settings (androidx.compose.material.icons.outlined  Box androidx.compose.material3  
CardElevation androidx.compose.material3  FloatingActionButton androidx.compose.material3  ImageVector androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  Switch androidx.compose.material3  	UserLogin androidx.compose.material3  bottomNavItems androidx.compose.material3  forEachIndexed androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  onBackground &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  Box androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  ImageVector androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  Scaffold androidx.compose.runtime  	UserLogin androidx.compose.runtime  bottomNavItems androidx.compose.runtime  forEachIndexed androidx.compose.runtime  ComposableFunction2 !androidx.compose.runtime.internal  	BottomEnd androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  clip androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Medium (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  ActivityData 4com.example.myapplication.features.main.presentation  ActivityItem 4com.example.myapplication.features.main.presentation  
BottomNavItem 4com.example.myapplication.features.main.presentation  Box 4com.example.myapplication.features.main.presentation  Card 4com.example.myapplication.features.main.presentation  CardDefaults 4com.example.myapplication.features.main.presentation  CircleShape 4com.example.myapplication.features.main.presentation  Color 4com.example.myapplication.features.main.presentation  ExperimentalMaterial3Api 4com.example.myapplication.features.main.presentation  FloatingActionButton 4com.example.myapplication.features.main.presentation  
HomeScreen 4com.example.myapplication.features.main.presentation  HomeScreenPreview 4com.example.myapplication.features.main.presentation  Icon 4com.example.myapplication.features.main.presentation  Icons 4com.example.myapplication.features.main.presentation  ImageVector 4com.example.myapplication.features.main.presentation  LazyRow 4com.example.myapplication.features.main.presentation  
MaterialTheme 4com.example.myapplication.features.main.presentation  
NavigationBar 4com.example.myapplication.features.main.presentation  NavigationBarItem 4com.example.myapplication.features.main.presentation  OptIn 4com.example.myapplication.features.main.presentation  RecentActivitiesSection 4com.example.myapplication.features.main.presentation  RecommendationCard 4com.example.myapplication.features.main.presentation  RecommendationData 4com.example.myapplication.features.main.presentation  RecommendationsSection 4com.example.myapplication.features.main.presentation  Row 4com.example.myapplication.features.main.presentation  Scaffold 4com.example.myapplication.features.main.presentation  StatCard 4com.example.myapplication.features.main.presentation  StatData 4com.example.myapplication.features.main.presentation  
StatsCards 4com.example.myapplication.features.main.presentation  	UserLogin 4com.example.myapplication.features.main.presentation  
WelcomeHeader 4com.example.myapplication.features.main.presentation  
background 4com.example.myapplication.features.main.presentation  bottomNavItems 4com.example.myapplication.features.main.presentation  
cardColors 4com.example.myapplication.features.main.presentation  
cardElevation 4com.example.myapplication.features.main.presentation  clip 4com.example.myapplication.features.main.presentation  forEach 4com.example.myapplication.features.main.presentation  forEachIndexed 4com.example.myapplication.features.main.presentation  getRecentActivities 4com.example.myapplication.features.main.presentation  getRecommendations 4com.example.myapplication.features.main.presentation  getStatsData 4com.example.myapplication.features.main.presentation  getValue 4com.example.myapplication.features.main.presentation  last 4com.example.myapplication.features.main.presentation  listOf 4com.example.myapplication.features.main.presentation  mutableStateOf 4com.example.myapplication.features.main.presentation  provideDelegate 4com.example.myapplication.features.main.presentation  remember 4com.example.myapplication.features.main.presentation  setValue 4com.example.myapplication.features.main.presentation  size 4com.example.myapplication.features.main.presentation  spacedBy 4com.example.myapplication.features.main.presentation  weight 4com.example.myapplication.features.main.presentation  width 4com.example.myapplication.features.main.presentation  color Acom.example.myapplication.features.main.presentation.ActivityData  icon Acom.example.myapplication.features.main.presentation.ActivityData  subtitle Acom.example.myapplication.features.main.presentation.ActivityData  time Acom.example.myapplication.features.main.presentation.ActivityData  title Acom.example.myapplication.features.main.presentation.ActivityData  selectedIcon Bcom.example.myapplication.features.main.presentation.BottomNavItem  title Bcom.example.myapplication.features.main.presentation.BottomNavItem  unselectedIcon Bcom.example.myapplication.features.main.presentation.BottomNavItem  description Gcom.example.myapplication.features.main.presentation.RecommendationData  title Gcom.example.myapplication.features.main.presentation.RecommendationData  color =com.example.myapplication.features.main.presentation.StatData  icon =com.example.myapplication.features.main.presentation.StatData  title =com.example.myapplication.features.main.presentation.StatData  value =com.example.myapplication.features.main.presentation.StatData  	Alignment =com.example.myapplication.features.notifications.presentation  Arrangement =com.example.myapplication.features.notifications.presentation  Boolean =com.example.myapplication.features.notifications.presentation  Box =com.example.myapplication.features.notifications.presentation  CardDefaults =com.example.myapplication.features.notifications.presentation  CircleShape =com.example.myapplication.features.notifications.presentation  Color =com.example.myapplication.features.notifications.presentation  Column =com.example.myapplication.features.notifications.presentation  
Composable =com.example.myapplication.features.notifications.presentation  
FontWeight =com.example.myapplication.features.notifications.presentation  Icon =com.example.myapplication.features.notifications.presentation  Icons =com.example.myapplication.features.notifications.presentation  ImageVector =com.example.myapplication.features.notifications.presentation  List =com.example.myapplication.features.notifications.presentation  
MaterialTheme =com.example.myapplication.features.notifications.presentation  Modifier =com.example.myapplication.features.notifications.presentation  NotificationCard =com.example.myapplication.features.notifications.presentation  NotificationItem =com.example.myapplication.features.notifications.presentation  NotificationSection =com.example.myapplication.features.notifications.presentation  NotificationType =com.example.myapplication.features.notifications.presentation  NotificationsScreen =com.example.myapplication.features.notifications.presentation  NotificationsScreenPreview =com.example.myapplication.features.notifications.presentation  Preview =com.example.myapplication.features.notifications.presentation  Row =com.example.myapplication.features.notifications.presentation  Spacer =com.example.myapplication.features.notifications.presentation  String =com.example.myapplication.features.notifications.presentation  Text =com.example.myapplication.features.notifications.presentation  
background =com.example.myapplication.features.notifications.presentation  
cardColors =com.example.myapplication.features.notifications.presentation  clip =com.example.myapplication.features.notifications.presentation  fillMaxWidth =com.example.myapplication.features.notifications.presentation  forEach =com.example.myapplication.features.notifications.presentation  getOlderNotifications =com.example.myapplication.features.notifications.presentation  getTodayNotifications =com.example.myapplication.features.notifications.presentation  getWeekNotifications =com.example.myapplication.features.notifications.presentation  height =com.example.myapplication.features.notifications.presentation  listOf =com.example.myapplication.features.notifications.presentation  padding =com.example.myapplication.features.notifications.presentation  size =com.example.myapplication.features.notifications.presentation  spacedBy =com.example.myapplication.features.notifications.presentation  weight =com.example.myapplication.features.notifications.presentation  width =com.example.myapplication.features.notifications.presentation  color Ncom.example.myapplication.features.notifications.presentation.NotificationItem  icon Ncom.example.myapplication.features.notifications.presentation.NotificationItem  isRead Ncom.example.myapplication.features.notifications.presentation.NotificationItem  message Ncom.example.myapplication.features.notifications.presentation.NotificationItem  time Ncom.example.myapplication.features.notifications.presentation.NotificationItem  title Ncom.example.myapplication.features.notifications.presentation.NotificationItem  ACHIEVEMENT Ncom.example.myapplication.features.notifications.presentation.NotificationType  BOOK_RECOMMENDATION Ncom.example.myapplication.features.notifications.presentation.NotificationType  READING_PROGRESS Ncom.example.myapplication.features.notifications.presentation.NotificationType  SOCIAL Ncom.example.myapplication.features.notifications.presentation.NotificationType  SYSTEM Ncom.example.myapplication.features.notifications.presentation.NotificationType  Achievement 7com.example.myapplication.features.profile.presentation  AchievementItem 7com.example.myapplication.features.profile.presentation  AchievementsSection 7com.example.myapplication.features.profile.presentation  	Alignment 7com.example.myapplication.features.profile.presentation  Arrangement 7com.example.myapplication.features.profile.presentation  BookItem 7com.example.myapplication.features.profile.presentation  Box 7com.example.myapplication.features.profile.presentation  CardDefaults 7com.example.myapplication.features.profile.presentation  CircleShape 7com.example.myapplication.features.profile.presentation  Color 7com.example.myapplication.features.profile.presentation  Column 7com.example.myapplication.features.profile.presentation  
Composable 7com.example.myapplication.features.profile.presentation  FavoriteBook 7com.example.myapplication.features.profile.presentation  FavoriteBooksSection 7com.example.myapplication.features.profile.presentation  Float 7com.example.myapplication.features.profile.presentation  
FontWeight 7com.example.myapplication.features.profile.presentation  Icon 7com.example.myapplication.features.profile.presentation  Icons 7com.example.myapplication.features.profile.presentation  ImageVector 7com.example.myapplication.features.profile.presentation  InfoItem 7com.example.myapplication.features.profile.presentation  LinearProgressIndicator 7com.example.myapplication.features.profile.presentation  
LogoutSection 7com.example.myapplication.features.profile.presentation  
MaterialTheme 7com.example.myapplication.features.profile.presentation  Modifier 7com.example.myapplication.features.profile.presentation  OutlinedButton 7com.example.myapplication.features.profile.presentation  PersonalInfoSection 7com.example.myapplication.features.profile.presentation  Preview 7com.example.myapplication.features.profile.presentation  
ProfileHeader 7com.example.myapplication.features.profile.presentation  
ProfileScreen 7com.example.myapplication.features.profile.presentation  ProfileScreenPreview 7com.example.myapplication.features.profile.presentation  ReadingStatsSection 7com.example.myapplication.features.profile.presentation  RoundedCornerShape 7com.example.myapplication.features.profile.presentation  Row 7com.example.myapplication.features.profile.presentation  Spacer 7com.example.myapplication.features.profile.presentation  StatItem 7com.example.myapplication.features.profile.presentation  String 7com.example.myapplication.features.profile.presentation  Text 7com.example.myapplication.features.profile.presentation  Unit 7com.example.myapplication.features.profile.presentation  	UserLogin 7com.example.myapplication.features.profile.presentation  androidx 7com.example.myapplication.features.profile.presentation  
background 7com.example.myapplication.features.profile.presentation  buttonColors 7com.example.myapplication.features.profile.presentation  
cardColors 7com.example.myapplication.features.profile.presentation  clip 7com.example.myapplication.features.profile.presentation  fillMaxWidth 7com.example.myapplication.features.profile.presentation  forEach 7com.example.myapplication.features.profile.presentation  getAchievements 7com.example.myapplication.features.profile.presentation  getFavoriteBooks 7com.example.myapplication.features.profile.presentation  height 7com.example.myapplication.features.profile.presentation  last 7com.example.myapplication.features.profile.presentation  listOf 7com.example.myapplication.features.profile.presentation  padding 7com.example.myapplication.features.profile.presentation  size 7com.example.myapplication.features.profile.presentation  spacedBy 7com.example.myapplication.features.profile.presentation  weight 7com.example.myapplication.features.profile.presentation  width 7com.example.myapplication.features.profile.presentation  color Ccom.example.myapplication.features.profile.presentation.Achievement  description Ccom.example.myapplication.features.profile.presentation.Achievement  icon Ccom.example.myapplication.features.profile.presentation.Achievement  title Ccom.example.myapplication.features.profile.presentation.Achievement  author Dcom.example.myapplication.features.profile.presentation.FavoriteBook  rating Dcom.example.myapplication.features.profile.presentation.FavoriteBook  title Dcom.example.myapplication.features.profile.presentation.FavoriteBook  	Alignment 8com.example.myapplication.features.settings.presentation  Arrangement 8com.example.myapplication.features.settings.presentation  Boolean 8com.example.myapplication.features.settings.presentation  Card 8com.example.myapplication.features.settings.presentation  CardDefaults 8com.example.myapplication.features.settings.presentation  Column 8com.example.myapplication.features.settings.presentation  
Composable 8com.example.myapplication.features.settings.presentation  
FontWeight 8com.example.myapplication.features.settings.presentation  Icon 8com.example.myapplication.features.settings.presentation  Icons 8com.example.myapplication.features.settings.presentation  ImageVector 8com.example.myapplication.features.settings.presentation  List 8com.example.myapplication.features.settings.presentation  
MaterialTheme 8com.example.myapplication.features.settings.presentation  Modifier 8com.example.myapplication.features.settings.presentation  Preview 8com.example.myapplication.features.settings.presentation  SettingItem 8com.example.myapplication.features.settings.presentation  SettingItemRow 8com.example.myapplication.features.settings.presentation  SettingType 8com.example.myapplication.features.settings.presentation  SettingsScreen 8com.example.myapplication.features.settings.presentation  SettingsScreenPreview 8com.example.myapplication.features.settings.presentation  SettingsSection 8com.example.myapplication.features.settings.presentation  Spacer 8com.example.myapplication.features.settings.presentation  String 8com.example.myapplication.features.settings.presentation  Switch 8com.example.myapplication.features.settings.presentation  Text 8com.example.myapplication.features.settings.presentation  
cardColors 8com.example.myapplication.features.settings.presentation  fillMaxWidth 8com.example.myapplication.features.settings.presentation  forEach 8com.example.myapplication.features.settings.presentation  getAboutSettings 8com.example.myapplication.features.settings.presentation  getAppearanceSettings 8com.example.myapplication.features.settings.presentation  getNotificationSettings 8com.example.myapplication.features.settings.presentation  getPrivacySettings 8com.example.myapplication.features.settings.presentation  
isNotEmpty 8com.example.myapplication.features.settings.presentation  listOf 8com.example.myapplication.features.settings.presentation  padding 8com.example.myapplication.features.settings.presentation  provideDelegate 8com.example.myapplication.features.settings.presentation  size 8com.example.myapplication.features.settings.presentation  spacedBy 8com.example.myapplication.features.settings.presentation  weight 8com.example.myapplication.features.settings.presentation  width 8com.example.myapplication.features.settings.presentation  icon Dcom.example.myapplication.features.settings.presentation.SettingItem  	isEnabled Dcom.example.myapplication.features.settings.presentation.SettingItem  subtitle Dcom.example.myapplication.features.settings.presentation.SettingItem  title Dcom.example.myapplication.features.settings.presentation.SettingItem  type Dcom.example.myapplication.features.settings.presentation.SettingItem  value Dcom.example.myapplication.features.settings.presentation.SettingItem  INFO Dcom.example.myapplication.features.settings.presentation.SettingType  
NAVIGATION Dcom.example.myapplication.features.settings.presentation.SettingType  SWITCH Dcom.example.myapplication.features.settings.presentation.SettingType  	Function2 kotlin  toString kotlin.Float  forEachIndexed kotlin.collections  last kotlin.collections  forEachIndexed kotlin.collections.List  isEmpty kotlin.collections.List  last kotlin.collections.List  last 
kotlin.ranges  forEachIndexed kotlin.sequences  last kotlin.sequences  forEachIndexed kotlin.text  last kotlin.text  com "androidx.compose.foundation.layout  Button +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  OutlinedButton +androidx.compose.foundation.layout.BoxScope  Refresh +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  Refresh .androidx.compose.foundation.layout.ColumnScope  
formatDate .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  example &androidx.compose.foundation.layout.com  
myapplication .androidx.compose.foundation.layout.com.example  core <androidx.compose.foundation.layout.com.example.myapplication  di Aandroidx.compose.foundation.layout.com.example.myapplication.core  AppContainer Dandroidx.compose.foundation.layout.com.example.myapplication.core.di  Refresh ,androidx.compose.material.icons.Icons.Filled  Refresh &androidx.compose.material.icons.filled  com androidx.compose.material3  example androidx.compose.material3.com  
myapplication &androidx.compose.material3.com.example  core 4androidx.compose.material3.com.example.myapplication  di 9androidx.compose.material3.com.example.myapplication.core  AppContainer <androidx.compose.material3.com.example.myapplication.core.di  com androidx.compose.runtime  example androidx.compose.runtime.com  
myapplication $androidx.compose.runtime.com.example  core 2androidx.compose.runtime.com.example.myapplication  di 7androidx.compose.runtime.com.example.myapplication.core  AppContainer :androidx.compose.runtime.com.example.myapplication.core.di  GetProfileUseCase !com.example.myapplication.core.di  ProfileRepository !com.example.myapplication.core.di  ProfileRepositoryImpl !com.example.myapplication.core.di  GetProfileUseCase .com.example.myapplication.core.di.AppContainer  ProfileRepositoryImpl .com.example.myapplication.core.di.AppContainer  getProfileUseCase .com.example.myapplication.core.di.AppContainer  profileRepository .com.example.myapplication.core.di.AppContainer  GET )com.example.myapplication.data.remote.api  ProfileResponse )com.example.myapplication.data.remote.api  
getProfile 4com.example.myapplication.data.remote.api.ApiService  com 4com.example.myapplication.features.main.presentation  example 8com.example.myapplication.features.main.presentation.com  
myapplication @com.example.myapplication.features.main.presentation.com.example  core Ncom.example.myapplication.features.main.presentation.com.example.myapplication  di Scom.example.myapplication.features.main.presentation.com.example.myapplication.core  AppContainer Vcom.example.myapplication.features.main.presentation.com.example.myapplication.core.di  Any :com.example.myapplication.features.profile.data.repository  
ApiService :com.example.myapplication.features.profile.data.repository  	AppLogger :com.example.myapplication.features.profile.data.repository  	Exception :com.example.myapplication.features.profile.data.repository  Gson :com.example.myapplication.features.profile.data.repository  
HttpException :com.example.myapplication.features.profile.data.repository  IOException :com.example.myapplication.features.profile.data.repository  Int :com.example.myapplication.features.profile.data.repository  Map :com.example.myapplication.features.profile.data.repository  ProfileErrorResponse :com.example.myapplication.features.profile.data.repository  ProfileRepository :com.example.myapplication.features.profile.data.repository  ProfileRepositoryImpl :com.example.myapplication.features.profile.data.repository  ProfileResponse :com.example.myapplication.features.profile.data.repository  Resource :com.example.myapplication.features.profile.data.repository  String :com.example.myapplication.features.profile.data.repository  System :com.example.myapplication.features.profile.data.repository  getInstance :com.example.myapplication.features.profile.data.repository  java :com.example.myapplication.features.profile.data.repository  	AppLogger Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  Gson Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  ProfileErrorResponse Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  Resource Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  System Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  
apiService Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  getInstance Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  gson Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  handleErrorResponse Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  java Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  logger Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  Boolean 7com.example.myapplication.features.profile.domain.model  Double 7com.example.myapplication.features.profile.domain.model  Int 7com.example.myapplication.features.profile.domain.model  Long 7com.example.myapplication.features.profile.domain.model  ProfileErrorResponse 7com.example.myapplication.features.profile.domain.model  ProfileRequestDto 7com.example.myapplication.features.profile.domain.model  ProfileResponse 7com.example.myapplication.features.profile.domain.model  ProfileStats 7com.example.myapplication.features.profile.domain.model  ReadingStats 7com.example.myapplication.features.profile.domain.model  String 7com.example.myapplication.features.profile.domain.model  message Lcom.example.myapplication.features.profile.domain.model.ProfileErrorResponse  	createdAt Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  email Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  fullName Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  isActive Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  role Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  stats Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  username Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  
averageRating Dcom.example.myapplication.features.profile.domain.model.ProfileStats  averageReadingTime Dcom.example.myapplication.features.profile.domain.model.ProfileStats  
favoriteGenre Dcom.example.myapplication.features.profile.domain.model.ProfileStats  let Dcom.example.myapplication.features.profile.domain.model.ProfileStats  readingStats Dcom.example.myapplication.features.profile.domain.model.ProfileStats  totalChaptersRead Dcom.example.myapplication.features.profile.domain.model.ProfileStats  totalReviews Dcom.example.myapplication.features.profile.domain.model.ProfileStats  	completed Dcom.example.myapplication.features.profile.domain.model.ReadingStats  
planToRead Dcom.example.myapplication.features.profile.domain.model.ReadingStats  reading Dcom.example.myapplication.features.profile.domain.model.ReadingStats  
totalWorks Dcom.example.myapplication.features.profile.domain.model.ReadingStats  Any <com.example.myapplication.features.profile.domain.repository  Map <com.example.myapplication.features.profile.domain.repository  ProfileRepository <com.example.myapplication.features.profile.domain.repository  ProfileResponse <com.example.myapplication.features.profile.domain.repository  Resource <com.example.myapplication.features.profile.domain.repository  String <com.example.myapplication.features.profile.domain.repository  
getProfile Ncom.example.myapplication.features.profile.domain.repository.ProfileRepository  	AppLogger 9com.example.myapplication.features.profile.domain.usecase  Boolean 9com.example.myapplication.features.profile.domain.usecase  	Exception 9com.example.myapplication.features.profile.domain.usecase  GetProfileUseCase 9com.example.myapplication.features.profile.domain.usecase  Map 9com.example.myapplication.features.profile.domain.usecase  ProfileRepository 9com.example.myapplication.features.profile.domain.usecase  ProfileResponse 9com.example.myapplication.features.profile.domain.usecase  Resource 9com.example.myapplication.features.profile.domain.usecase  String 9com.example.myapplication.features.profile.domain.usecase  format 9com.example.myapplication.features.profile.domain.usecase  getInstance 9com.example.myapplication.features.profile.domain.usecase  isBlank 9com.example.myapplication.features.profile.domain.usecase  let 9com.example.myapplication.features.profile.domain.usecase  mapOf 9com.example.myapplication.features.profile.domain.usecase  to 9com.example.myapplication.features.profile.domain.usecase  	AppLogger Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  Resource Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  format Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  getFormattedStats Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  getInstance Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  
hasValidStats Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  invoke Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  isBlank Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  let Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  logger Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  mapOf Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  profileRepository Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  to Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  validateProfileData Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  Error Bcom.example.myapplication.features.profile.domain.usecase.Resource  Loading Bcom.example.myapplication.features.profile.domain.usecase.Resource  Success Bcom.example.myapplication.features.profile.domain.usecase.Resource  AppContainer 7com.example.myapplication.features.profile.presentation  	AppLogger 7com.example.myapplication.features.profile.presentation  Boolean 7com.example.myapplication.features.profile.presentation  Button 7com.example.myapplication.features.profile.presentation  Card 7com.example.myapplication.features.profile.presentation  CircularProgressIndicator 7com.example.myapplication.features.profile.presentation  Class 7com.example.myapplication.features.profile.presentation  ErrorScreen 7com.example.myapplication.features.profile.presentation  	Exception 7com.example.myapplication.features.profile.presentation  GetProfileUseCase 7com.example.myapplication.features.profile.presentation  
IconButton 7com.example.myapplication.features.profile.presentation  IllegalArgumentException 7com.example.myapplication.features.profile.presentation  
LoadingScreen 7com.example.myapplication.features.profile.presentation  Map 7com.example.myapplication.features.profile.presentation  MutableStateFlow 7com.example.myapplication.features.profile.presentation  ProfileContent 7com.example.myapplication.features.profile.presentation  ProfileResponse 7com.example.myapplication.features.profile.presentation  ProfileUiState 7com.example.myapplication.features.profile.presentation  ProfileViewModel 7com.example.myapplication.features.profile.presentation  ProfileViewModelFactory 7com.example.myapplication.features.profile.presentation  Resource 7com.example.myapplication.features.profile.presentation  	StateFlow 7com.example.myapplication.features.profile.presentation  Suppress 7com.example.myapplication.features.profile.presentation  T 7com.example.myapplication.features.profile.presentation  	TextAlign 7com.example.myapplication.features.profile.presentation  	ViewModel 7com.example.myapplication.features.profile.presentation  ViewModelProvider 7com.example.myapplication.features.profile.presentation  _uiState 7com.example.myapplication.features.profile.presentation  asStateFlow 7com.example.myapplication.features.profile.presentation  emptyMap 7com.example.myapplication.features.profile.presentation  
formatDate 7com.example.myapplication.features.profile.presentation  getInstance 7com.example.myapplication.features.profile.presentation  getProfileUseCase 7com.example.myapplication.features.profile.presentation  java 7com.example.myapplication.features.profile.presentation  launch 7com.example.myapplication.features.profile.presentation  let 7com.example.myapplication.features.profile.presentation  logger 7com.example.myapplication.features.profile.presentation  provideDelegate 7com.example.myapplication.features.profile.presentation  split 7com.example.myapplication.features.profile.presentation  	uppercase 7com.example.myapplication.features.profile.presentation  copy Fcom.example.myapplication.features.profile.presentation.ProfileUiState  errorMessage Fcom.example.myapplication.features.profile.presentation.ProfileUiState  formattedStats Fcom.example.myapplication.features.profile.presentation.ProfileUiState  hasStats Fcom.example.myapplication.features.profile.presentation.ProfileUiState  	isLoading Fcom.example.myapplication.features.profile.presentation.ProfileUiState  profile Fcom.example.myapplication.features.profile.presentation.ProfileUiState  	AppLogger Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  MutableStateFlow Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  ProfileUiState Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  _uiState Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  asStateFlow Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  
clearError Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  getInstance Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  getProfileUseCase Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  launch Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  loadProfile Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  logger Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  refreshProfile Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  uiState Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  viewModelScope Hcom.example.myapplication.features.profile.presentation.ProfileViewModel  IllegalArgumentException Ocom.example.myapplication.features.profile.presentation.ProfileViewModelFactory  ProfileViewModel Ocom.example.myapplication.features.profile.presentation.ProfileViewModelFactory  appContainer Ocom.example.myapplication.features.profile.presentation.ProfileViewModelFactory  java Ocom.example.myapplication.features.profile.presentation.ProfileViewModelFactory  Error @com.example.myapplication.features.profile.presentation.Resource  Loading @com.example.myapplication.features.profile.presentation.Resource  Success @com.example.myapplication.features.profile.presentation.Resource  Factory Icom.example.myapplication.features.profile.presentation.ViewModelProvider  name java.lang.Class  to kotlin  let 
kotlin.Double  	compareTo kotlin.Float  div kotlin.Float  toFloat 
kotlin.Int  toString 
kotlin.Int  format 
kotlin.String  split 
kotlin.String  to 
kotlin.String  Map kotlin.collections  emptyMap kotlin.collections  mapOf kotlin.collections  get kotlin.collections.List  size kotlin.collections.List  get kotlin.collections.Map  format kotlin.text  split kotlin.text  getProfileUseCase !kotlinx.coroutines.CoroutineScope  GET retrofit2.http  ProfileResponse retrofit2.http  avatar Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  id Gcom.example.myapplication.features.profile.domain.model.ProfileResponse  
isNullOrBlank 9com.example.myapplication.features.profile.domain.usecase  
isNullOrBlank Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  
isNullOrBlank 
kotlin.String  
isNullOrBlank kotlin.text  println :com.example.myapplication.features.profile.data.repository  println Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  println 	kotlin.io  println 9com.example.myapplication.features.profile.domain.usecase  println Kcom.example.myapplication.features.profile.domain.usecase.GetProfileUseCase  	AppLogger &com.example.myapplication.core.network  getInstance &com.example.myapplication.core.network  	AppLogger 6com.example.myapplication.core.network.AuthInterceptor  getInstance 6com.example.myapplication.core.network.AuthInterceptor  	AppLogger @com.example.myapplication.core.network.AuthInterceptor.Companion  getInstance @com.example.myapplication.core.network.AuthInterceptor.Companion  logger 6com.example.myapplication.core.network.AuthInterceptor  let 4com.example.myapplication.core.security.TokenManager  com :com.example.myapplication.features.profile.data.repository  let :com.example.myapplication.features.profile.data.repository  take :com.example.myapplication.features.profile.data.repository  let Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  take Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  tokenManager Pcom.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl  example >com.example.myapplication.features.profile.data.repository.com  
myapplication Fcom.example.myapplication.features.profile.data.repository.com.example  core Tcom.example.myapplication.features.profile.data.repository.com.example.myapplication  security Ycom.example.myapplication.features.profile.data.repository.com.example.myapplication.core  TokenManager bcom.example.myapplication.features.profile.data.repository.com.example.myapplication.core.security  take 
kotlin.String  take kotlin.collections  Sequence kotlin.sequences  take kotlin.sequences  take kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             