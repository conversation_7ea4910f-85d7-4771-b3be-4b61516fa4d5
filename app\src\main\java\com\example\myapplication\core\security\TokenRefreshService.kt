package com.example.myapplication.core.security

import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.minutes

/**
 * Serviço para refresh automático de tokens
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar refresh automático
 * Seguindo OCP: Extensível para diferentes estratégias de refresh
 */
interface ITokenRefreshService {
    fun startAutoRefresh()
    fun stopAutoRefresh()
    fun scheduleRefresh(delayMinutes: Long)
    suspend fun checkAndRefreshIfNeeded(): Boolean
}

class TokenRefreshService(
    private val tokenManager: TokenManager,
    private val refreshTokenUseCase: RefreshTokenUseCase
) : ITokenRefreshService {
    
    private val logger = AppLogger.getInstance()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var refreshJob: Job? = null
    
    companion object {
        private const val CHECK_INTERVAL_MINUTES = 15L // Verificar a cada 15 minutos
        private const val REFRESH_THRESHOLD_MINUTES = 30L // Renovar se expira em menos de 30 min
    }
    
    override fun startAutoRefresh() {
        logger.info("TokenRefreshService", "Iniciando serviço de refresh automático de token")

        stopAutoRefresh() // Parar job anterior se existir

        refreshJob = serviceScope.launch {
            while (isActive) {
                try {
                    checkAndRefreshIfNeeded()
                    delay(CHECK_INTERVAL_MINUTES.minutes)
                } catch (e: Exception) {
                    logger.error("TokenRefreshService", "Erro no serviço de refresh automático", e)
                    delay(CHECK_INTERVAL_MINUTES.minutes) // Continuar tentando
                }
            }
        }
    }

    override fun stopAutoRefresh() {
        refreshJob?.cancel()
        refreshJob = null
        logger.info("TokenRefreshService", "Serviço de refresh automático parado")
    }

    override fun scheduleRefresh(delayMinutes: Long) {
        logger.info("TokenRefreshService", "Agendando refresh de token em $delayMinutes minutos")
        
        serviceScope.launch {
            delay(delayMinutes.minutes)
            checkAndRefreshIfNeeded()
        }
    }
    
    override suspend fun checkAndRefreshIfNeeded(): Boolean {
        if (!tokenManager.isAuthenticated.value) {
            logger.debug("TokenRefreshService", "Usuário não autenticado, pulando verificação de refresh")
            return false
        }

        return try {
            val timeToExpire = getTimeToTokenExpiration()

            if (timeToExpire != null && timeToExpire <= REFRESH_THRESHOLD_MINUTES) {
                logger.info("TokenRefreshService", "Token expira em $timeToExpire minutos, iniciando refresh")

                val refreshResult = refreshTokenUseCase()
                if (refreshResult) {
                    logger.auth("Token renovado com sucesso")
                    true
                } else {
                    logger.auth("Falha na renovação do token")
                    logger.warning("TokenRefreshService", "Falha no refresh automático, usuário pode precisar fazer login novamente")
                    false
                }
            } else {
                logger.debug("TokenRefreshService", "Token ainda válido por ${timeToExpire ?: "tempo desconhecido"} minutos")
                true
            }
        } catch (e: Exception) {
            logger.error("TokenRefreshService", "Erro ao verificar necessidade de refresh", e)
            false
        }
    }
    
    /**
     * Calcula tempo em minutos até a expiração do token
     * @return minutos até expiração ou null se não conseguir calcular
     */
    private suspend fun getTimeToTokenExpiration(): Long? {
        return try {
            val tokenInfo = tokenManager.getTokenExpirationInfo()
            if (tokenInfo == null) return null

            val (tokenSavedAt, expiresIn) = tokenInfo
            val currentTime = System.currentTimeMillis()
            val tokenExpirationTime = tokenSavedAt + (expiresIn * 1000)
            val timeToExpire = (tokenExpirationTime - currentTime) / 1000 / 60 // minutos

            maxOf(0, timeToExpire) // Não retornar valores negativos
        } catch (e: Exception) {
            logger.error("TokenRefreshService", "Erro ao calcular tempo de expiração do token", e)
            null
        }
    }

    /**
     * Força refresh imediato (útil para testes ou situações específicas)
     */
    suspend fun forceRefresh(): Boolean {
        logger.info("TokenRefreshService", "Forçando refresh de token")
        return try {
            val result = refreshTokenUseCase()
            if (result) {
                logger.auth("Token renovado com sucesso")
            } else {
                logger.auth("Falha na renovação do token")
            }
            result
        } catch (e: Exception) {
            logger.error("TokenRefreshService", "Erro ao forçar refresh de token", e)
            false
        }
    }
    
    /**
     * Obtém informações sobre o status do token
     */
    suspend fun getTokenStatus(): TokenStatus {
        if (!tokenManager.isAuthenticated.value) {
            return TokenStatus.NOT_AUTHENTICATED
        }
        
        val timeToExpire = getTimeToTokenExpiration()
        return when {
            timeToExpire == null -> TokenStatus.INVALID
            timeToExpire <= 0 -> TokenStatus.EXPIRED
            timeToExpire <= REFRESH_THRESHOLD_MINUTES -> TokenStatus.NEEDS_REFRESH
            else -> TokenStatus.VALID
        }
    }
}

/**
 * Status do token para facilitar decisões
 */
enum class TokenStatus {
    NOT_AUTHENTICATED,
    INVALID,
    EXPIRED,
    NEEDS_REFRESH,
    VALID
}

/**
 * Factory para criar TokenRefreshService
 */
object TokenRefreshServiceFactory {
    
    fun create(
        tokenManager: TokenManager,
        refreshTokenUseCase: RefreshTokenUseCase
    ): TokenRefreshService {
        return TokenRefreshService(
            tokenManager = tokenManager,
            refreshTokenUseCase = refreshTokenUseCase
        )
    }
}
