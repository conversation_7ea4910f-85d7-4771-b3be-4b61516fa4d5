package com.example.myapplication.features.profile.data.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.features.profile.domain.model.ProfileErrorResponse
import com.example.myapplication.features.profile.domain.model.ProfileResponse
import com.example.myapplication.features.profile.domain.repository.ProfileRepository
import com.google.gson.Gson
import retrofit2.HttpException
import java.io.IOException

/**
 * Implementação do repositório de perfil
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar dados de perfil
 * Seguindo DIP: Implementa a abstração ProfileRepository
 */
class ProfileRepositoryImpl(
    private val apiService: ApiService,
    private val tokenManager: com.example.myapplication.core.security.TokenManager? = null
) : ProfileRepository {
    
    private val logger = AppLogger.getInstance()
    private val gson = Gson()
    
    override suspend fun getProfile(): Resource<ProfileResponse> {
        println("getProfile")
        println("apiService: $apiService")
        return try {
            println("getProfile - Iniciando requisição")

            // Debug: verificar token antes da requisição
            tokenManager?.let { tm ->
                val token = tm.getAccessToken()
                val isValid = tm.isTokenValid()
                val isAuth = tm.isAuthenticated.value
                println("DEBUG TOKEN - Token: ${token?.take(20)}..., Valid: $isValid, Authenticated: $isAuth")
                logger.debug("ProfileRepository", "Token status - Valid: $isValid, Authenticated: $isAuth, Token length: ${token?.length ?: 0}")
            }

            logger.network("GET request para: auth/profile")
            val startTime = System.currentTimeMillis()

            val response = apiService.getProfile()
            println("getProfile - Requisição concluída")
            println("response: $response")
            val duration = System.currentTimeMillis() - startTime

            if (response.isSuccessful) {
                val profileData = response.body()
                println("profileData: $profileData")
                if (profileData != null) {
                    logger.network("GET response de: auth/profile - Status: ${response.code()} - Duração: ${duration}ms")
                    logger.info("ProfileRepository", "Perfil carregado com sucesso para usuário: ${profileData.username}")
                    logger.debug("ProfileRepository", "Dados do perfil: ID=${profileData.id}, Username=${profileData.username}, Email=${profileData.email}, FullName='${profileData.fullName}', HasStats=${profileData.stats != null}")
                    Resource.Success(profileData)
                } else {
                    logger.warning("ProfileRepository", "Resposta de perfil vazia")
                    Resource.Error("Dados de perfil não encontrados")
                }
            } else {
                val errorMessage = handleErrorResponse(response.errorBody()?.string(), response.code())
                logger.network("Erro na requisição GET para: auth/profile - Erro: $errorMessage")
                Resource.Error(errorMessage)
            }
            
        } catch (e: HttpException) {
            val errorMessage = "Erro de rede: ${e.message()}"
            logger.error("ProfileRepository", errorMessage, e)
            Resource.Error(errorMessage)
        } catch (e: IOException) {
            val errorMessage = "Erro de conexão: Verifique sua internet"
            logger.error("ProfileRepository", errorMessage, e)
            Resource.Error(errorMessage)
        } catch (e: Exception) {
            val errorMessage = "Erro inesperado: ${e.message}"
            logger.error("ProfileRepository", errorMessage, e)
            Resource.Error(errorMessage)
        }
    }
    
    override suspend fun updateProfile(profileData: Map<String, Any>): Resource<ProfileResponse> {
        // TODO: Implementar quando endpoint estiver disponível
        return Resource.Error("Funcionalidade não implementada ainda")
    }
    
    override suspend fun updateAvatar(avatarUrl: String): Resource<ProfileResponse> {
        // TODO: Implementar quando endpoint estiver disponível
        return Resource.Error("Funcionalidade não implementada ainda")
    }
    
    /**
     * Trata erros da resposta da API
     */
    private fun handleErrorResponse(errorBody: String?, statusCode: Int): String {
        return try {
            if (errorBody != null) {
                val errorResponse = gson.fromJson(errorBody, ProfileErrorResponse::class.java)
                when (statusCode) {
                    401 -> "Sessão expirada. Faça login novamente."
                    403 -> "Acesso negado ao perfil."
                    404 -> "Perfil não encontrado."
                    500 -> "Erro interno do servidor. Tente novamente."
                    else -> errorResponse.message
                }
            } else {
                when (statusCode) {
                    401 -> "Sessão expirada. Faça login novamente."
                    403 -> "Acesso negado."
                    404 -> "Recurso não encontrado."
                    500 -> "Erro interno do servidor."
                    else -> "Erro desconhecido (código: $statusCode)"
                }
            }
        } catch (e: Exception) {
            logger.warning("ProfileRepository", "Erro ao parsear resposta de erro: ${e.message}")
            when (statusCode) {
                401 -> "Sessão expirada. Faça login novamente."
                403 -> "Acesso negado."
                404 -> "Recurso não encontrado."
                500 -> "Erro interno do servidor."
                else -> "Erro de comunicação com o servidor"
            }
        }
    }
}
