package com.example.myapplication.core.security

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.domain.model.AuthToken
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.RegisterResponse
import com.example.myapplication.domain.model.UserLogin
import com.example.myapplication.features.auth.domain.usecase.LogoutUseCase
import com.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase
import kotlinx.coroutines.flow.StateFlow

/**
 * Gerenciador central de autenticação
 * 
 * Seguindo SRP: Responsabilidade única de coordenar autenticação
 * Seguindo OCP: Extensível para novos fluxos de autenticação
 * Seguindo DIP: Depende de abstrações (TokenManager, UseCases)
 * 
 * Funcionalidades:
 * - Coordena login/logout/refresh
 * - Gerencia estado de autenticação
 * - Integra com TokenManager para persistência
 * - Fornece interface unificada para UI
 */
interface IAuthenticationManagerService {
    val isAuthenticated: StateFlow<Boolean>
    val currentUser: StateFlow<UserLogin?>
    
    suspend fun handleLoginSuccess(loginResponse: LoginResponse): Resource<Unit>
    suspend fun handleRegisterSuccess(registerResponse: RegisterResponse): Resource<Unit>
    suspend fun logout(): Resource<Unit>
    suspend fun refreshTokenIfNeeded(): Boolean
    suspend fun checkAuthenticationStatus(): Boolean
    suspend fun startAutoRefresh()
    suspend fun stopAutoRefresh()
}

class AuthenticationManager(
    private val tokenManager: TokenManager,
    private val refreshTokenUseCase: RefreshTokenUseCase,
    private val logoutUseCase: LogoutUseCase,
    private val tokenRefreshService: TokenRefreshService? = null
) : IAuthenticationManagerService {

    private val logger = AppLogger.getInstance()

    override val isAuthenticated: StateFlow<Boolean> = tokenManager.isAuthenticated
    override val currentUser: StateFlow<UserLogin?> = tokenManager.currentUser
    
    /**
     * Processa sucesso de login salvando tokens e atualizando estado
     */
    override suspend fun handleLoginSuccess(loginResponse: LoginResponse): Resource<Unit> {
        return try {
            val authToken = AuthToken(
                accessToken = loginResponse.accessToken,
                refreshToken = loginResponse.refreshToken,
                tokenType = loginResponse.tokenType,
                expiresIn = loginResponse.expiresIn
            )
            
            tokenManager.saveTokens(authToken, loginResponse.user)
            Resource.Success(Unit)
            
        } catch (e: Exception) {
            Resource.Error("Erro ao salvar dados de autenticação: ${e.message}")
        }
    }
    
    /**
     * Processa sucesso de registro salvando tokens e atualizando estado
     */
    override suspend fun handleRegisterSuccess(registerResponse: RegisterResponse): Resource<Unit> {
        return try {
            val authToken = AuthToken(
                accessToken = registerResponse.accessToken,
                refreshToken = registerResponse.refreshToken,
                tokenType = registerResponse.tokenType,
                expiresIn = registerResponse.expiresIn
            )
            
            tokenManager.saveTokens(authToken, registerResponse.user)
            Resource.Success(Unit)
            
        } catch (e: Exception) {
            Resource.Error("Erro ao salvar dados de registro: ${e.message}")
        }
    }
    
    /**
     * Executa logout completo
     */
    override suspend fun logout(): Resource<Unit> {
        return logoutUseCase()
    }
    
    /**
     * Verifica se refresh é necessário e executa se for o caso
     */
    override suspend fun refreshTokenIfNeeded(): Boolean {
        return if (!tokenManager.isTokenValid()) {
            refreshTokenUseCase()
        } else {
            true // Token já é válido
        }
    }
    
    /**
     * Verifica status de autenticação na inicialização
     * Útil para determinar tela inicial (login vs main)
     */
    override suspend fun checkAuthenticationStatus(): Boolean {
        return try {
            logger.logInfo("Verificando status de autenticação")

            if (tokenManager.isAuthenticated.value) {
                // Verificar se token ainda é válido
                if (tokenManager.isTokenValid()) {
                    logger.logInfo("Token válido encontrado")
                    true
                } else {
                    logger.logInfo("Token expirado, tentando refresh")
                    // Tentar refresh
                    refreshTokenIfNeeded()
                }
            } else {
                logger.logInfo("Usuário não autenticado")
                false
            }
        } catch (e: Exception) {
            logger.logError("Erro ao verificar status de autenticação", e)
            false
        }
    }
    
    /**
     * Obtém dados do usuário atual
     */
    fun getCurrentUser(): UserLogin? {
        return currentUser.value
    }
    
    /**
     * Verifica se usuário está autenticado
     */
    fun isUserAuthenticated(): Boolean {
        return isAuthenticated.value
    }
    
    /**
     * Força refresh de token (útil para testes ou situações específicas)
     */
    suspend fun forceRefresh(): Resource<Boolean> {
        return refreshTokenUseCase.forceRefresh()
    }

    /**
     * Inicia o serviço de refresh automático de token
     */
    override suspend fun startAutoRefresh() {
        logger.logInfo("Iniciando refresh automático de token")
        tokenRefreshService?.startAutoRefresh()
    }

    /**
     * Para o serviço de refresh automático de token
     */
    override suspend fun stopAutoRefresh() {
        logger.logInfo("Parando refresh automático de token")
        tokenRefreshService?.stopAutoRefresh()
    }
}

/**
 * Factory para criar AuthenticationManager
 */
object AuthenticationManagerFactory {
    
    fun create(
        tokenManager: TokenManager,
        refreshTokenUseCase: RefreshTokenUseCase,
        logoutUseCase: LogoutUseCase,
        tokenRefreshService: TokenRefreshService? = null
    ): AuthenticationManager {
        return AuthenticationManager(
            tokenManager = tokenManager,
            refreshTokenUseCase = refreshTokenUseCase,
            logoutUseCase = logoutUseCase,
            tokenRefreshService = tokenRefreshService
        )
    }
}
