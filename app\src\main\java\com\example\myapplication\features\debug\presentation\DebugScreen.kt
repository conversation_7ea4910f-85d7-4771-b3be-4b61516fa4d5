package com.example.myapplication.features.debug.presentation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.core.security.TokenStatus
import com.example.myapplication.ui.theme.MyApplicationTheme
import kotlinx.coroutines.launch

/**
 * Tela de debug para testar cenários edge do sistema de autenticação
 * 
 * Seguindo SRP: Responsabilidade única de facilitar testes e debug
 */
@Composable
fun DebugScreen(
    appContainer: AppContainer,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val coroutineScope = rememberCoroutineScope()
    val isAuthenticated by appContainer.authenticationManager.isAuthenticated.collectAsState()
    val currentUser by appContainer.authenticationManager.currentUser.collectAsState()
    
    val lastTestResult = remember { mutableStateOf<String?>(null) }
    val tokenStatus = remember { mutableStateOf<TokenStatus?>(null) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Text(
            text = "Debug - Sistema de Autenticação",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold
        )
        
        // Status atual
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = if (isAuthenticated) 
                    MaterialTheme.colorScheme.primaryContainer 
                else 
                    MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Status Atual",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text("Autenticado: ${if (isAuthenticated) "Sim" else "Não"}")
                Text("Usuário: ${currentUser?.username ?: "N/A"}")
                Text("Token Status: ${tokenStatus.value?.name ?: "Verificando..."}")
            }
        }
        
        // Testes de cenários edge
        Text(
            text = "Testes de Cenários Edge",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold
        )
        
        // Teste 1: Verificar status do token
        Button(
            onClick = {
                coroutineScope.launch {
                    try {
                        val status = appContainer.tokenRefreshService.getTokenStatus()
                        tokenStatus.value = status
                        lastTestResult.value = "Status do token: ${status.name}"
                    } catch (e: Exception) {
                        lastTestResult.value = "Erro ao verificar status: ${e.message}"
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Verificar Status do Token")
        }
        
        // Teste 2: Forçar refresh de token
        Button(
            onClick = {
                coroutineScope.launch {
                    try {
                        val result = appContainer.tokenRefreshService.forceRefresh()
                        lastTestResult.value = "Refresh forçado: ${if (result) "Sucesso" else "Falha"}"
                    } catch (e: Exception) {
                        lastTestResult.value = "Erro no refresh: ${e.message}"
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = isAuthenticated
        ) {
            Text("Forçar Refresh de Token")
        }
        
        // Teste 3: Verificar validade do token
        Button(
            onClick = {
                coroutineScope.launch {
                    try {
                        val isValid = appContainer.tokenManager.isTokenValid()
                        lastTestResult.value = "Token válido: ${if (isValid) "Sim" else "Não"}"
                    } catch (e: Exception) {
                        lastTestResult.value = "Erro na verificação: ${e.message}"
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = isAuthenticated
        ) {
            Text("Verificar Validade do Token")
        }
        
        // Teste 4: Simular token expirado (limpar dados)
        OutlinedButton(
            onClick = {
                coroutineScope.launch {
                    try {
                        appContainer.tokenManager.clearTokens()
                        lastTestResult.value = "Tokens limpos - simulando expiração"
                    } catch (e: Exception) {
                        lastTestResult.value = "Erro ao limpar tokens: ${e.message}"
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Simular Token Expirado")
        }
        
        // Teste 5: Verificar informações de expiração
        Button(
            onClick = {
                coroutineScope.launch {
                    try {
                        val tokenInfo = appContainer.tokenManager.getTokenExpirationInfo()
                        if (tokenInfo != null) {
                            val (savedAt, expiresIn) = tokenInfo
                            val currentTime = System.currentTimeMillis()
                            val expirationTime = savedAt + (expiresIn * 1000)
                            val timeLeft = (expirationTime - currentTime) / 1000 / 60 // minutos
                            lastTestResult.value = "Token expira em: $timeLeft minutos"
                        } else {
                            lastTestResult.value = "Informações de expiração não disponíveis"
                        }
                    } catch (e: Exception) {
                        lastTestResult.value = "Erro ao obter info de expiração: ${e.message}"
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = isAuthenticated
        ) {
            Text("Verificar Tempo de Expiração")
        }
        
        // Resultado do último teste
        lastTestResult.value?.let { result ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Resultado do Último Teste",
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(result)
                }
            }
        }
        
        // Botão para voltar
        Spacer(modifier = Modifier.height(16.dp))
        OutlinedButton(
            onClick = onNavigateBack,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Voltar")
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DebugScreenPreview() {
    MyApplicationTheme {
        // Preview não pode usar AppContainer real
        Text("Debug Screen Preview")
    }
}
