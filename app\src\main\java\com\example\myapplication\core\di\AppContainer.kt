package com.example.myapplication.core.di

import android.content.Context
import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.core.network.AuthInterceptor
import com.example.myapplication.core.security.AuthenticationManager
import com.example.myapplication.core.security.AuthenticationManagerFactory
import com.example.myapplication.core.security.TokenManager
import com.example.myapplication.core.security.TokenRefreshService
import com.example.myapplication.core.security.TokenRefreshServiceFactory
import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.data.repository.AuthRepositoryImpl
import com.example.myapplication.domain.repository.AuthRepository
import com.example.myapplication.domain.useCase.LoginUseCase
import com.example.myapplication.domain.useCase.RegisterUseCase
import com.example.myapplication.features.auth.domain.usecase.LogoutUseCase
import com.example.myapplication.features.auth.domain.usecase.RefreshTokenUseCase
import com.example.myapplication.features.profile.data.repository.ProfileRepositoryImpl
import com.example.myapplication.features.profile.domain.repository.ProfileRepository
import com.example.myapplication.features.profile.domain.usecase.GetProfileUseCase
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Container manual para Dependency Injection
 *
 * Seguindo DIP: Dependemos de abstrações (interfaces) não de implementações concretas
 * Seguindo SRP: Responsabilidade única de prover dependências
 */
class AppContainer(private val context: Context) {

    // Security dependencies
    val tokenManager: TokenManager by lazy {
        TokenManager.getInstance(context)
    }

    // Network dependencies
    private val authInterceptor: AuthInterceptor by lazy {
        AuthInterceptor(tokenManager)
    }

    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .addInterceptor(authInterceptor)
            .connectTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .build()
    }
    
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(AppConstants.Api.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val apiService: ApiService by lazy {
        retrofit.create(ApiService::class.java)
    }
    
    // Repository dependencies
    val authRepository: AuthRepository by lazy {
        AuthRepositoryImpl(apiService)
    }

    val profileRepository: ProfileRepository by lazy {
        ProfileRepositoryImpl(apiService)
    }
    
    // UseCase dependencies
    val loginUseCase: LoginUseCase by lazy {
        LoginUseCase(authRepository)
    }

    val registerUseCase: RegisterUseCase by lazy {
        RegisterUseCase(authRepository)
    }

    val refreshTokenUseCase: RefreshTokenUseCase by lazy {
        RefreshTokenUseCase(authRepository, tokenManager)
    }

    val logoutUseCase: LogoutUseCase by lazy {
        LogoutUseCase(tokenManager)
    }

    val getProfileUseCase: GetProfileUseCase by lazy {
        GetProfileUseCase(profileRepository)
    }

    // Token Refresh Service
    val tokenRefreshService: TokenRefreshService by lazy {
        TokenRefreshServiceFactory.create(
            tokenManager = tokenManager,
            refreshTokenUseCase = refreshTokenUseCase
        )
    }

    // Authentication Manager
    val authenticationManager: AuthenticationManager by lazy {
        AuthenticationManagerFactory.create(
            tokenManager = tokenManager,
            refreshTokenUseCase = refreshTokenUseCase,
            logoutUseCase = logoutUseCase,
            tokenRefreshService = tokenRefreshService
        )
    }
}
