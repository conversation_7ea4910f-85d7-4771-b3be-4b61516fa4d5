package com.example.myapplication.features.profile.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.features.profile.domain.model.ProfileResponse
import com.example.myapplication.features.profile.domain.usecase.GetProfileUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel para a tela de perfil
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar estado da tela de perfil
 * Seguindo DIP: Depende da abstração GetProfileUseCase
 */
class ProfileViewModel(
    private val getProfileUseCase: GetProfileUseCase
) : ViewModel() {
    
    private val logger = AppLogger.getInstance()
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()
    
    init {
        loadProfile()
    }
    
    /**
     * Carrega o perfil do usuário
     */
    fun loadProfile() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                errorMessage = null
            )
            
            try {
                when (val result = getProfileUseCase()) {
                    is Resource.Success -> {
                        val profile = result.data!!
                        val formattedStats = getProfileUseCase.getFormattedStats(profile)
                        
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            profile = profile,
                            formattedStats = formattedStats,
                            hasStats = getProfileUseCase.hasValidStats(profile),
                            errorMessage = null
                        )
                        
                        logger.info("ProfileViewModel", "Perfil carregado com sucesso para: ${profile.username}")
                    }
                    is Resource.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = result.message,
                            profile = null
                        )
                        
                        logger.warning("ProfileViewModel", "Erro ao carregar perfil: ${result.message}")
                    }
                    is Resource.Loading -> {
                        // Estado já definido acima
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Erro inesperado: ${e.message}",
                    profile = null
                )
                
                logger.error("ProfileViewModel", "Erro inesperado ao carregar perfil", e)
            }
        }
    }
    
    /**
     * Recarrega o perfil (pull-to-refresh)
     */
    fun refreshProfile() {
        logger.info("ProfileViewModel", "Recarregando perfil do usuário")
        loadProfile()
    }
    
    /**
     * Limpa mensagem de erro
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * Obtém estatística específica formatada
     */
    fun getStatValue(key: String): String {
        return _uiState.value.formattedStats[key] ?: "N/A"
    }
    
    /**
     * Verifica se o perfil está carregado
     */
    fun isProfileLoaded(): Boolean {
        return _uiState.value.profile != null
    }
    
    /**
     * Obtém progresso da meta de leitura (exemplo: completed / totalWorks)
     */
    fun getReadingProgress(): Float {
        val profile = _uiState.value.profile
        return if (profile?.stats != null) {
            val completed = profile.stats.readingStats.completed.toFloat()
            val total = profile.stats.readingStats.totalWorks.toFloat()
            if (total > 0) completed / total else 0f
        } else {
            0f
        }
    }
    
    /**
     * Obtém texto do progresso de leitura
     */
    fun getReadingProgressText(): String {
        val profile = _uiState.value.profile
        return if (profile?.stats != null) {
            val completed = profile.stats.readingStats.completed
            val total = profile.stats.readingStats.totalWorks
            "$completed/$total livros"
        } else {
            "0/0 livros"
        }
    }
}

/**
 * Estado da UI do perfil
 */
data class ProfileUiState(
    val isLoading: Boolean = false,
    val profile: ProfileResponse? = null,
    val formattedStats: Map<String, String> = emptyMap(),
    val hasStats: Boolean = false,
    val errorMessage: String? = null
)
