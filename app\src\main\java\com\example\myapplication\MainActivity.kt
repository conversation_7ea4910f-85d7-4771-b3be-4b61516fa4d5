package com.example.myapplication

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.navigation.AppNavigation
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.login.LoginViewModelFactory
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterResult
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory
import com.example.myapplication.features.auth.presentation.network.NetworkTestScreen
import com.example.myapplication.features.main.presentation.MainScreen
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Activity principal da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de configurar a UI e navegação básica
 * Seguindo OCP: Facilmente extensível para novas funcionalidades
 */
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            MyApplicationTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    AppNavigation(
                        onShowToast = { message ->
                            Toast.makeText(this@MainActivity, message, Toast.LENGTH_LONG).show()
                        },
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}
