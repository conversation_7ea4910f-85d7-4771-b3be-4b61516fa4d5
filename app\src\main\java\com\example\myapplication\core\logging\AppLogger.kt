package com.example.myapplication.core.logging

import android.util.Log
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Implementação do sistema de logging da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar logs
 * Seguindo OCP: Extensível para diferentes destinos de log
 */
class AppLogger private constructor(
    private val config: LogConfig = LogConfig()
) : ILoggerService {
    
    companion object {
        @Volatile
        private var INSTANCE: AppLogger? = null
        
        // Tags específicas para diferentes módulos
        const val TAG_AUTH = "AUTH"
        const val TAG_NETWORK = "NETWORK"
        const val TAG_SECURITY = "SECURITY"
        const val TAG_TOKEN = "TOKEN"
        const val TAG_NAVIGATION = "NAVIGATION"
        const val TAG_UI = "UI"
        const val TAG_DATABASE = "DATABASE"
        
        fun getInstance(config: LogConfig = LogConfig()): AppLogger {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AppLogger(config).also { INSTANCE = it }
            }
        }
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    override fun debug(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(LogLevel.DEBUG)) {
            logToConsole(LogLevel.DEBUG, tag, message, throwable)
        }
    }
    
    override fun info(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(LogLevel.INFO)) {
            logToConsole(LogLevel.INFO, tag, message, throwable)
        }
    }
    
    override fun warning(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(LogLevel.WARNING)) {
            logToConsole(LogLevel.WARNING, tag, message, throwable)
        }
    }
    
    override fun error(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(LogLevel.ERROR)) {
            logToConsole(LogLevel.ERROR, tag, message, throwable)
        }
    }
    
    override fun critical(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(LogLevel.CRITICAL)) {
            logToConsole(LogLevel.CRITICAL, tag, message, throwable)
        }
    }
    
    override fun auth(message: String, throwable: Throwable?) {
        info(TAG_AUTH, message, throwable)
    }
    
    override fun network(message: String, throwable: Throwable?) {
        info(TAG_NETWORK, message, throwable)
    }
    
    override fun security(message: String, throwable: Throwable?) {
        warning(TAG_SECURITY, message, throwable)
    }
    
    /**
     * Verifica se deve fazer log baseado no nível configurado
     */
    private fun shouldLog(level: LogLevel): Boolean {
        return level.priority >= config.minLevel.priority
    }
    
    /**
     * Faz log no console do Android (Logcat)
     */
    private fun logToConsole(level: LogLevel, tag: String, message: String, throwable: Throwable?) {
        if (!config.enableConsoleLogging) return
        
        val timestamp = dateFormat.format(Date())
        val formattedMessage = "[$timestamp] $message"
        
        when (level) {
            LogLevel.DEBUG -> {
                Log.d(tag, formattedMessage, throwable)
            }
            LogLevel.INFO -> {
                Log.i(tag, formattedMessage, throwable)
            }
            LogLevel.WARNING -> {
                Log.w(tag, formattedMessage, throwable)
            }
            LogLevel.ERROR -> {
                Log.e(tag, formattedMessage, throwable)
            }
            LogLevel.CRITICAL -> {
                Log.wtf(tag, formattedMessage, throwable)
            }
        }
    }
    
    /**
     * Métodos de conveniência para logging específico de autenticação
     */
    fun logLoginAttempt(username: String) {
        auth("Tentativa de login para usuário: $username")
    }
    
    fun logLoginSuccess(username: String) {
        auth("Login bem-sucedido para usuário: $username")
    }
    
    fun logLoginFailure(username: String, reason: String) {
        auth("Falha no login para usuário: $username. Motivo: $reason")
    }
    
    fun logLogout(username: String?) {
        auth("Logout realizado para usuário: ${username ?: "desconhecido"}")
    }
    
    fun logTokenRefresh(success: Boolean) {
        if (success) {
            auth("Token renovado com sucesso")
        } else {
            auth("Falha na renovação do token")
        }
    }
    
    fun logTokenExpiration() {
        auth("Token expirado detectado")
    }
    
    fun logAuthenticationCheck(isAuthenticated: Boolean) {
        auth("Verificação de autenticação: ${if (isAuthenticated) "autenticado" else "não autenticado"}")
    }
    
    /**
     * Métodos de conveniência para logging de rede
     */
    fun logNetworkRequest(method: String, url: String) {
        network("$method request para: $url")
    }
    
    fun logNetworkResponse(method: String, url: String, statusCode: Int, duration: Long) {
        network("$method response de: $url - Status: $statusCode - Duração: ${duration}ms")
    }
    
    fun logNetworkError(method: String, url: String, error: String) {
        network("Erro na requisição $method para: $url - Erro: $error")
    }
    
    /**
     * Métodos de conveniência para logging de segurança
     */
    fun logSecurityEvent(event: String, details: String? = null) {
        val message = if (details != null) "$event - Detalhes: $details" else event
        security(message)
    }
    
    fun logSuspiciousActivity(activity: String, context: String) {
        security("Atividade suspeita detectada: $activity - Contexto: $context")
    }
}

/**
 * Extensões para facilitar o uso do logger
 */
fun Any.logDebug(message: String, throwable: Throwable? = null) {
    AppLogger.getInstance().debug(this::class.java.simpleName, message, throwable)
}

fun Any.logInfo(message: String, throwable: Throwable? = null) {
    AppLogger.getInstance().info(this::class.java.simpleName, message, throwable)
}

fun Any.logWarning(message: String, throwable: Throwable? = null) {
    AppLogger.getInstance().warning(this::class.java.simpleName, message, throwable)
}

fun Any.logError(message: String, throwable: Throwable? = null) {
    AppLogger.getInstance().error(this::class.java.simpleName, message, throwable)
}

fun Any.logCritical(message: String, throwable: Throwable? = null) {
    AppLogger.getInstance().critical(this::class.java.simpleName, message, throwable)
}
