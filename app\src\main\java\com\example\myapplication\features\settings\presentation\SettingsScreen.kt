package com.example.myapplication.features.settings.presentation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Shield
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Storage
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Tela de configurações da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar configurações
 */
@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Configurações",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        item {
            SettingsSection(
                title = "Aparência",
                items = getAppearanceSettings()
            )
        }
        
        item {
            SettingsSection(
                title = "Notificações",
                items = getNotificationSettings()
            )
        }
        
        item {
            SettingsSection(
                title = "Privacidade e Segurança",
                items = getPrivacySettings()
            )
        }
        
        item {
            SettingsSection(
                title = "Sobre",
                items = getAboutSettings()
            )
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    items: List<SettingItem>
) {
    Column {
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(4.dp)
            ) {
                items.forEach { item ->
                    SettingItemRow(item = item)
                }
            }
        }
    }
}

@Composable
private fun SettingItemRow(item: SettingItem) {
    var isEnabled by remember { mutableStateOf(item.isEnabled) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            if (item.subtitle.isNotEmpty()) {
                Text(
                    text = item.subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
        
        when (item.type) {
            SettingType.SWITCH -> {
                Switch(
                    checked = isEnabled,
                    onCheckedChange = { isEnabled = it }
                )
            }
            SettingType.NAVIGATION -> {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
            SettingType.INFO -> {
                Text(
                    text = item.value,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

// Data classes
data class SettingItem(
    val title: String,
    val subtitle: String = "",
    val icon: ImageVector,
    val type: SettingType,
    val isEnabled: Boolean = false,
    val value: String = ""
)

enum class SettingType {
    SWITCH,
    NAVIGATION,
    INFO
}

// Dados mockados
private fun getAppearanceSettings() = listOf(
    SettingItem(
        title = "Tema Escuro",
        subtitle = "Ativar modo escuro",
        icon = Icons.Default.DarkMode,
        type = SettingType.SWITCH,
        isEnabled = false
    ),
    SettingItem(
        title = "Idioma",
        subtitle = "Português (Brasil)",
        icon = Icons.Default.Language,
        type = SettingType.NAVIGATION
    )
)

private fun getNotificationSettings() = listOf(
    SettingItem(
        title = "Notificações Push",
        subtitle = "Receber notificações do app",
        icon = Icons.Default.Notifications,
        type = SettingType.SWITCH,
        isEnabled = true
    ),
    SettingItem(
        title = "Notificações por Email",
        subtitle = "Receber emails sobre atividades",
        icon = Icons.Default.Notifications,
        type = SettingType.SWITCH,
        isEnabled = false
    )
)

private fun getPrivacySettings() = listOf(
    SettingItem(
        title = "Privacidade",
        subtitle = "Gerenciar dados pessoais",
        icon = Icons.Default.Shield,
        type = SettingType.NAVIGATION
    ),
    SettingItem(
        title = "Segurança",
        subtitle = "Configurações de segurança",
        icon = Icons.Default.Security,
        type = SettingType.NAVIGATION
    ),
    SettingItem(
        title = "Dados e Armazenamento",
        subtitle = "Gerenciar cache e dados",
        icon = Icons.Default.Storage,
        type = SettingType.NAVIGATION
    )
)

private fun getAboutSettings() = listOf(
    SettingItem(
        title = "Versão do App",
        icon = Icons.Default.Storage,
        type = SettingType.INFO,
        value = "1.0.0"
    ),
    SettingItem(
        title = "Termos de Uso",
        icon = Icons.Default.Shield,
        type = SettingType.NAVIGATION
    ),
    SettingItem(
        title = "Política de Privacidade",
        icon = Icons.Default.Security,
        type = SettingType.NAVIGATION
    )
)

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    MyApplicationTheme {
        SettingsScreen()
    }
}
