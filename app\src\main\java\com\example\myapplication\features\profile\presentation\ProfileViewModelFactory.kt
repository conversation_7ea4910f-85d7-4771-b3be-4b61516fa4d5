package com.example.myapplication.features.profile.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.core.di.AppContainer

/**
 * Factory para criar instâncias do ProfileViewModel
 * 
 * Seguindo DIP: Usa o AppContainer para resolver dependências
 * Seguindo SRP: Responsabilidade única de criar ViewModels de perfil
 */
class ProfileViewModelFactory(
    private val appContainer: AppContainer
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ProfileViewModel::class.java)) {
            return ProfileViewModel(
                getProfileUseCase = appContainer.getProfileUseCase
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}
