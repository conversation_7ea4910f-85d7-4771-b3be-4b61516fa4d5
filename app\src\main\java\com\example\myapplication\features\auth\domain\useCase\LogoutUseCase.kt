package com.example.myapplication.features.auth.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.security.TokenManager

/**
 * Caso de uso para logout do usuário
 * 
 * Seguindo SRP: Responsabilidade única de executar logout seguro
 * Seguindo DIP: Depende da abstração TokenManager
 * 
 * Funcionalidades:
 * - Limpa todos os tokens armazenados
 * - Reseta estado de autenticação
 * - Opcionalmente notifica servidor sobre logout
 * - Garante limpeza completa de dados sensíveis
 */
class LogoutUseCase(
    private val tokenManager: TokenManager
) {
    
    /**
     * Executa logout completo do usuário
     * 
     * @param notifyServer Se deve notificar o servidor sobre o logout
     * @return Resource indicando sucesso ou falha do logout
     */
    suspend operator fun invoke(notifyServer: Boolean = false): Resource<Unit> {
        return try {
            // TODO: Se notifyServer = true, fazer chamada para endpoint de logout
            // val result = authRepository.logout()
            
            // Limpar tokens e estado local independentemente da resposta do servidor
            tokenManager.clearTokens()
            
            Resource.Success(Unit)
            
        } catch (e: Exception) {
            // Mesmo em caso de erro, garantir que tokens sejam limpos
            try {
                tokenManager.clearTokens()
            } catch (clearException: Exception) {
                // Log do erro de limpeza, mas não falhar o logout
            }
            
            Resource.Error("Erro durante logout: ${e.message}")
        }
    }
    
    /**
     * Logout silencioso - apenas limpa dados locais sem notificar servidor
     * Útil para casos de token expirado ou erro de autenticação
     */
    suspend fun silentLogout(): Resource<Unit> {
        return invoke(notifyServer = false)
    }
    
    /**
     * Verifica se o usuário está logado antes de fazer logout
     */
    suspend fun logoutIfAuthenticated(): Resource<Unit> {
        return if (tokenManager.isAuthenticated.value) {
            invoke()
        } else {
            Resource.Success(Unit) // Já deslogado
        }
    }
}
