package com.example.myapplication.data.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.data.remote.dto.LoginRequestDto
import com.example.myapplication.data.remote.dto.RegisterRequestDto
import com.example.myapplication.data.remote.dto.ErrorResponseDto
import com.example.myapplication.domain.model.AuthError
import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.RegisterRequest
import com.example.myapplication.domain.model.RegisterResponse
import com.example.myapplication.domain.model.UserLogin
import com.example.myapplication.domain.repository.AuthRepository
import com.google.gson.Gson
import retrofit2.HttpException
import java.io.IOException

/**
 * Implementação do repositório de autenticação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar dados de autenticação
 * Seguindo DIP: Implementa a abstração AuthRepository
 */
class AuthRepositoryImpl(
    private val apiService: ApiService
) : AuthRepository {

    override suspend fun login(loginRequest: LoginRequest): Resource<LoginResponse> {
        return try {
            val requestDto = LoginRequestDto(
                usernameOrEmail = loginRequest.usernameOrEmail,
                password = loginRequest.password
            )
            val response = apiService.login(requestDto)
            if (response.isSuccessful) handleSuccessResponse(response.body())
            else handleErrorResponse(response.errorBody()?.string(), response.code())
            
        } catch (e: HttpException) {
            Resource.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            Resource.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            Resource.Error("Erro inesperado: ${e.message}")
        }
    }

    private fun handleSuccessResponse(baseResponse: Any?): Resource<LoginResponse> {
        val response = baseResponse as? com.example.myapplication.data.remote.dto.BaseResponseDto<*>
            ?: return Resource.Error("Resposta vazia do servidor")
        if (!response.success || response.data == null)  return Resource.Error(response.message ?: "Operação não foi bem-sucedida")
        val loginResponseDto = response.data as com.example.myapplication.data.remote.dto.LoginResponseDto
        val userDto = loginResponseDto.user 
            ?: return Resource.Error("Dados do usuário não encontrados na resposta")
        val loginResponse = LoginResponse(
            accessToken = loginResponseDto.accessToken,
            refreshToken = loginResponseDto.refreshToken,
            tokenType = loginResponseDto.tokenType,
            expiresIn = loginResponseDto.expiresIn,
            user = UserLogin(
                id = userDto.id,
                username = userDto.username,
                email = userDto.email,
                fullName = userDto.fullName,
                avatar = userDto.avatar,
                isActive = userDto.isActive,
                createdAt = userDto.createdAt,
                updatedAt = userDto.updatedAt
            )
        )
        return Resource.Success(loginResponse)
    }

    private fun handleErrorResponse(errorBody: String?, statusCode: Int): Resource<LoginResponse> {
        val authError = try {
            val errorResponse = Gson().fromJson(errorBody, ErrorResponseDto::class.java)
            val message = when (errorResponse.message) {
                is String -> errorResponse.message
                is List<*> -> (errorResponse.message as List<String>).joinToString(", ")
                else -> "Erro desconhecido"
            }
            AuthError(
                statusCode = errorResponse.statusCode,
                message = message,
                error = errorResponse.error,
                code = errorResponse.code
            )
        } catch (e: Exception) {
            AuthError(
                statusCode = statusCode,
                message = "Erro de autenticação",
                error = "Bad Request"
            )
        }
        return Resource.Error(authError.message)
    }

    override suspend fun register(registerRequest: RegisterRequest): Resource<RegisterResponse> {
        return try {
            val requestDto = RegisterRequestDto(
                username = registerRequest.username,
                email = registerRequest.email,
                password = registerRequest.password,
                fullName = registerRequest.fullName
            )
            val response = apiService.register(requestDto)
            if (response.isSuccessful) handleRegisterSuccessResponse(response.body())
            else handleRegisterErrorResponse(response.errorBody()?.string(), response.code())

        } catch (e: HttpException) {
            Resource.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            Resource.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            Resource.Error("Erro inesperado: ${e.message}")
        }
    }

    private fun handleRegisterSuccessResponse(baseResponse: Any?): Resource<RegisterResponse> {
        val response = baseResponse as? com.example.myapplication.data.remote.dto.BaseResponseDto<*>
            ?: return Resource.Error("Resposta vazia do servidor")
        if (!response.success || response.data == null) return Resource.Error(response.message ?: "Operação não foi bem-sucedida")
        val loginResponseDto = response.data as com.example.myapplication.data.remote.dto.LoginResponseDto
        val userDto = loginResponseDto.user
            ?: return Resource.Error("Dados do usuário não encontrados na resposta")
        val registerResponse = RegisterResponse(
            accessToken = loginResponseDto.accessToken,
            refreshToken = loginResponseDto.refreshToken,
            tokenType = loginResponseDto.tokenType,
            expiresIn = loginResponseDto.expiresIn,
            user = UserLogin(
                id = userDto.id,
                username = userDto.username,
                email = userDto.email,
                fullName = userDto.fullName,
                avatar = userDto.avatar,
                isActive = userDto.isActive,
                createdAt = userDto.createdAt,
                updatedAt = userDto.updatedAt
            )
        )
        return Resource.Success(registerResponse)
    }

    private fun handleRegisterErrorResponse(errorBody: String?, statusCode: Int): Resource<RegisterResponse> {
        val authError = try {
            val errorResponse = Gson().fromJson(errorBody, ErrorResponseDto::class.java)
            val message = when (errorResponse.message) {
                is String -> errorResponse.message
                is List<*> -> (errorResponse.message as List<String>).joinToString(", ")
                else -> "Erro desconhecido"
            }
            AuthError(
                statusCode = errorResponse.statusCode,
                message = message,
                error = errorResponse.error,
                code = errorResponse.code
            )
        } catch (e: Exception) {
            AuthError(
                statusCode = statusCode,
                message = "Erro de registro",
                error = "Bad Request"
            )
        }
        return Resource.Error(authError.message)
    }

    override suspend fun refreshToken(refreshToken: String): Resource<LoginResponse> {
        return try {
            // TODO: Implementar endpoint de refresh no ApiService
            // Por enquanto, retornar erro para indicar que precisa ser implementado
            Resource.Error("Endpoint de refresh token ainda não implementado")

        } catch (e: HttpException) {
            Resource.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            Resource.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            Resource.Error("Erro inesperado: ${e.message}")
        }
    }
}
