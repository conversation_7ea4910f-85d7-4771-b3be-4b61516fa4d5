package com.example.myapplication.core.network

import com.example.myapplication.core.logging.AppLogger
import com.example.myapplication.core.security.TokenManager
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * Interceptador de autenticação para injeção automática de tokens
 * 
 * Seguindo SRP: Responsabilidade única de adicionar tokens às requisições
 * Seguindo OCP: Extensível para diferentes tipos de autenticação
 * 
 * Funcionalidades:
 * - Adiciona automaticamente Authorization header com Bearer token
 * - Ignora endpoints que não precisam de autenticação
 * - Integra com TokenManager para obter tokens válidos
 * - Thread-safe para uso com Retrofit/OkHttp
 */
class AuthInterceptor(
    private val tokenManager: TokenManager
) : Interceptor {

    private val logger = AppLogger.getInstance()

    private val logger = AppLogger.getInstance()
    
    companion object {
        /**
         * Endpoints que não precisam de autenticação
         * Seguindo OCP: Facilmente extensível para novos endpoints
         */
        private val OPEN_ENDPOINTS = listOf(
            "auth/login",
            "auth/register",
            "auth/refresh"
        )
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestUrl = originalRequest.url.toString()
        
        // Verificar se o endpoint precisa de autenticação
        val needsAuth = OPEN_ENDPOINTS.none { endpoint ->
            requestUrl.contains(endpoint)
        }
        
        if (!needsAuth) {
            // Endpoint público, prosseguir sem token
            return chain.proceed(originalRequest)
        }
        
        // Obter token de acesso
        val accessToken = runBlocking {
            try {
                tokenManager.getAccessToken()
            } catch (e: Exception) {
                null
            }
        }
        
        if (accessToken.isNullOrEmpty()) {
            // Sem token válido, prosseguir sem autenticação
            // A API retornará 401 se necessário
            return chain.proceed(originalRequest)
        }
        
        // Adicionar Authorization header
        val authenticatedRequest = originalRequest.newBuilder()
            .header("Authorization", "Bearer $accessToken")
            .build()
        
        return chain.proceed(authenticatedRequest)
    }
}

/**
 * Interceptador avançado com refresh automático de tokens
 * 
 * Funcionalidades adicionais:
 * - Detecta respostas 401 (Unauthorized)
 * - Tenta refresh automático do token
 * - Reexecuta requisição original com novo token
 * - Evita loops infinitos de refresh
 */
class AuthInterceptorWithRefresh(
    private val tokenManager: TokenManager,
    private val refreshTokenUseCase: suspend () -> Boolean
) : Interceptor {
    
    companion object {
        private val OPEN_ENDPOINTS = listOf(
            "auth/login",
            "auth/register",
            "auth/refresh"
        )
    }
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestUrl = originalRequest.url.toString()
        
        // Verificar se o endpoint precisa de autenticação
        val needsAuth = OPEN_ENDPOINTS.none { endpoint ->
            requestUrl.contains(endpoint)
        }
        
        if (!needsAuth) {
            return chain.proceed(originalRequest)
        }
        
        // Primeira tentativa com token atual
        val response = proceedWithAuth(chain, originalRequest)
        
        // Se recebeu 401 e não é endpoint de refresh, tentar renovar token
        if (response.code == 401 && !requestUrl.contains("auth/refresh")) {
            response.close() // Importante: fechar response anterior
            
            val refreshSuccess = runBlocking {
                try {
                    refreshTokenUseCase()
                } catch (e: Exception) {
                    false
                }
            }
            
            if (refreshSuccess) {
                // Tentar novamente com novo token
                return proceedWithAuth(chain, originalRequest)
            }
        }
        
        return response
    }
    
    private fun proceedWithAuth(chain: Interceptor.Chain, originalRequest: okhttp3.Request): Response {
        val accessToken = runBlocking {
            try {
                tokenManager.getAccessToken()
            } catch (e: Exception) {
                null
            }
        }
        
        val request = if (!accessToken.isNullOrEmpty()) {
            originalRequest.newBuilder()
                .header("Authorization", "Bearer $accessToken")
                .build()
        } else {
            originalRequest
        }
        
        return chain.proceed(request)
    }
}

/**
 * Factory para criar interceptadores de autenticação
 * 
 * Seguindo Factory Pattern para facilitar criação e configuração
 */
object AuthInterceptorFactory {
    
    /**
     * Cria interceptador básico apenas com injeção de token
     */
    fun createBasic(tokenManager: TokenManager): AuthInterceptor {
        return AuthInterceptor(tokenManager)
    }
    
    /**
     * Cria interceptador avançado com refresh automático
     */
    fun createWithRefresh(
        tokenManager: TokenManager,
        refreshTokenUseCase: suspend () -> Boolean
    ): AuthInterceptorWithRefresh {
        return AuthInterceptorWithRefresh(tokenManager, refreshTokenUseCase)
    }
}
