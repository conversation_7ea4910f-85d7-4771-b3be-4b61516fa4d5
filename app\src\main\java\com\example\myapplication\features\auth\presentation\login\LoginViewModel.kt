package com.example.myapplication.features.auth.presentation.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.Resource
import com.example.myapplication.domain.useCase.LoginUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel para a tela de login
 *
 * Seguindo SRP: Responsabilidade única de gerenciar o estado da tela de login
 * Seguindo DIP: Depende da abstração LoginUseCase, não da implementação
 */
class LoginViewModel(
    private val loginUseCase: LoginUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    fun onEvent(event: LoginEvent) {
        when (event) {
            is LoginEvent.UsernameOrEmailChanged -> {
                _uiState.value = _uiState.value.copy(
                    usernameOrEmail = event.value,
                    errorMessage = null
                )
            }
            is LoginEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    errorMessage = null
                )
            }
            is LoginEvent.TogglePasswordVisibility -> {
                _uiState.value = _uiState.value.copy(
                    isPasswordVisible = !_uiState.value.isPasswordVisible
                )
            }
            is LoginEvent.Login -> {
                performLogin()
            }
            is LoginEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null,
                    loginResult = null
                )
            }
        }
    }

    private fun performLogin() {
        val currentState = _uiState.value
        
        if (currentState.isLoading) return
        
        _uiState.value = currentState.copy(
            isLoading = true,
            errorMessage = null,
            loginResult = null
        )

        viewModelScope.launch {
            val result = loginUseCase(
                usernameOrEmail = currentState.usernameOrEmail,
                password = currentState.password
            )

            when (result) {
                is Resource.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        loginResult = LoginResult.Success(result.data!!)
                    )
                }
                is Resource.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = result.message,
                        loginResult = LoginResult.Error(result.message ?: "Erro desconhecido")
                    )
                }
                is Resource.Loading -> {
                    // Estado já definido acima
                }
            }
        }
    }
}
