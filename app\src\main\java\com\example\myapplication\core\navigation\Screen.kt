package com.example.myapplication.core.navigation

/**
 * Definição das rotas de navegação da aplicação
 * 
 * Seguindo OCP: Fácil de estender com novas rotas sem modificar as existentes
 */
sealed class Screen(val route: String) {

    // Splash Feature
    object Splash : Screen("splash")

    // Auth Feature
    object Login : Screen("login")
    object Register : Screen("register")
    object NetworkTest : Screen("network_test")

    // Main Feature
    object Main : Screen("main")
    object Home : Screen("home")
    object Profile : Screen("profile")
    object Settings : Screen("settings")
    object Notifications : Screen("notifications")

    // Debug Feature
    object Debug : Screen("debug")
    
    // Navegação com argumentos
    object UserProfile : Screen("user/{userId}") {
        fun createRoute(userId: String) = "user/$userId"
    }
}

/**
 * Graphs de navegação para organizar features
 */
object NavigationGraph {
    const val AUTH = "auth_graph"
    const val MAIN = "main_graph"
    const val ROOT = "root_graph"
}
